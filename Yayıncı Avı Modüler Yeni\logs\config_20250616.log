2025-06-16 13:06:12,813 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:761) (automation_worker.py:251)
2025-06-16 13:06:12,821 [INFO] __main__ - 📁 Çalışma dizini: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:252)
2025-06-16 13:06:12,823 [INFO] __main__ - 🐍 Python versiyonu: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)] (automation_worker.py:253)
2025-06-16 13:23:35,016 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 13:23:35,017 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı A<PERSON><PERSON> (automation_worker.py:253)
2025-06-16 13:23:35,028 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 13:23:35,043 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 13:23:35,044 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 13:23:45,183 [INFO] __main__ - 📨 Komut: start (ID: 351) (automation_worker.py:214)
2025-06-16 13:23:45,284 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 13:23:48,568 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 13:23:48,568 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 13:23:48,568 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 13:23:48,571 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 13:23:48,572 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 13:23:48,572 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 13:23:48,572 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:234)
2025-06-16 13:23:48,573 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:291)
2025-06-16 13:23:48,576 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 351) (automation_worker.py:150)
2025-06-16 13:23:51,084 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:291)
2025-06-16 13:25:24,686 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 13:25:24,686 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 13:25:24,702 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 13:25:24,702 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 13:25:24,702 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 13:25:32,748 [INFO] __main__ - 📨 Komut: start (ID: 353) (automation_worker.py:214)
2025-06-16 13:25:32,748 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 13:25:35,905 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 13:25:35,905 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 13:25:35,905 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 13:25:35,905 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 13:25:35,905 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 13:25:35,905 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 13:25:35,905 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:234)
2025-06-16 13:25:35,905 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:291)
2025-06-16 13:25:35,905 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 353) (automation_worker.py:150)
2025-06-16 13:25:38,077 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:291)
2025-06-16 13:25:40,060 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:291)
2025-06-16 13:25:51,686 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:291)
2025-06-16 13:25:55,515 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 342 (scraper_thread.py:291)
2025-06-16 13:26:10,811 [INFO] scraper_thread - [SCRAPER] reberr2101 | 0 (scraper_thread.py:291)
2025-06-16 13:26:14,828 [INFO] scraper_thread - [SCRAPER] reelsye4 | 6 (scraper_thread.py:291)
2025-06-16 13:26:18,780 [INFO] scraper_thread - [SCRAPER] sekofitness | 287 (scraper_thread.py:291)
2025-06-16 13:26:27,405 [INFO] scraper_thread - [SCRAPER] zazaokeypubg | 28 (scraper_thread.py:291)
2025-06-16 13:26:34,702 [INFO] scraper_thread - [SCRAPER] bbcberkan | 91 (scraper_thread.py:291)
2025-06-16 13:26:39,342 [INFO] scraper_thread - [SCRAPER] thisisdusuncetozu | 50 (scraper_thread.py:291)
2025-06-16 13:26:44,796 [INFO] scraper_thread - [SCRAPER] eros.pm4 | 16 (scraper_thread.py:291)
2025-06-16 13:26:49,436 [INFO] scraper_thread - [SCRAPER] yelizzz1907 | 93 (scraper_thread.py:291)
2025-06-16 13:26:53,421 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 9 benzersiz kullanıcı bulundu (scraper_thread.py:291)
2025-06-16 13:26:56,905 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome driver kapatıldı (scraper_thread.py:291)
2025-06-16 13:26:57,045 [INFO] scraper_thread - [SCRAPER] 🔄 Tüm Chrome işlemleri kapatıldı (scraper_thread.py:291)
2025-06-16 13:27:02,498 [INFO] main - 🏁 scraper tamamlandı (main.py:113)
2025-06-16 13:27:02,498 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:27:05,655 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 13:27:08,670 [INFO] main - 2️⃣ Status Checker başlatılıyor (8 kullanıcı) (main.py:179)
2025-06-16 13:29:55,702 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 13:29:55,702 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 13:29:55,702 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:148)
2025-06-16 13:29:55,702 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:29:55,873 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 13:31:46,090 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 13:31:46,090 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 13:31:46,103 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 13:31:46,117 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 13:31:46,118 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 13:32:23,405 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 13:32:23,405 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 13:32:23,420 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 13:32:23,436 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 13:32:23,436 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 13:32:28,952 [INFO] __main__ - 📨 Komut: start (ID: 354) (automation_worker.py:214)
2025-06-16 13:32:28,952 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 13:32:29,456 [INFO] __main__ - 📨 Komut: start (ID: 354) (automation_worker.py:214)
2025-06-16 13:32:29,457 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 13:32:32,217 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 13:32:32,217 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 13:32:32,217 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 13:32:32,217 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 13:32:32,217 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 13:32:32,217 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 13:32:32,217 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:234)
2025-06-16 13:32:32,217 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:333)
2025-06-16 13:32:32,217 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 354) (automation_worker.py:150)
2025-06-16 13:32:33,092 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 13:32:33,092 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 13:32:33,092 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 13:32:33,092 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 13:32:33,092 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 13:32:33,092 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 13:32:33,092 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:234)
2025-06-16 13:32:33,092 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:333)
2025-06-16 13:32:33,092 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 354) (automation_worker.py:150)
2025-06-16 13:32:34,420 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:333)
2025-06-16 13:32:35,358 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:333)
2025-06-16 13:32:39,525 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:333)
2025-06-16 13:32:39,526 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:333)
2025-06-16 13:32:40,251 [ERROR] scraper_thread - [SCRAPER] ❌ Chrome başlatma hatası: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x0x7ff6555dfea5+79173]
	GetHandleVerifier [0x0x7ff6555dff00+79264]
	(No symbol) [0x0x7ff655399e5a]
	(No symbol) [0x0x7ff6553d7b85]
	(No symbol) [0x0x7ff6553d2b1d]
	(No symbol) [0x0x7ff6554267de]
	(No symbol) [0x0x7ff655425f70]
	(No symbol) [0x0x7ff655418743]
	(No symbol) [0x0x7ff6553e14c1]
	(No symbol) [0x0x7ff6553e2253]
	GetHandleVerifier [0x0x7ff6558aa2dd+3004797]
	GetHandleVerifier [0x0x7ff6558a472d+2981325]
	GetHandleVerifier [0x0x7ff6558c3380+3107360]
	GetHandleVerifier [0x0x7ff6555faa2e+188622]
	GetHandleVerifier [0x0x7ff6556022bf+219487]
	GetHandleVerifier [0x0x7ff6555e8df4+115860]
	GetHandleVerifier [0x0x7ff6555e8fa9+116297]
	GetHandleVerifier [0x0x7ff6555cf558+11256]
	BaseThreadInitThunk [0x0x7ff80e937374+20]
	RtlUserThreadStart [0x0x7ff81081cc91+33]
 (scraper_thread.py:337)
2025-06-16 13:32:45,077 [ERROR] scraper_thread - [SCRAPER] Chrome başlatılamadı! İşlem sonlandırılıyor. (scraper_thread.py:337)
2025-06-16 13:32:47,468 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:333)
2025-06-16 13:32:51,350 [INFO] main - 🏁 scraper tamamlandı (main.py:113)
2025-06-16 13:32:51,368 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:32:51,874 [WARNING] urllib3.connectionpool - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', ConnectionResetError(10054, 'Varolan bir bağlantı uzaktaki bir ana bilgisayar tarafından zorla kapatıldı', None, 10054, None))': /session/bf10fbdf79a4464ebc28d34b3699c70f/url (connectionpool.py:827)
2025-06-16 13:32:54,811 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 13:32:56,055 [WARNING] urllib3.connectionpool - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000020382643910>: Failed to establish a new connection: [WinError 10061] Hedef makine etkin olarak reddettiğinden bağlantı kurulamadı')': /session/bf10fbdf79a4464ebc28d34b3699c70f/url (connectionpool.py:827)
2025-06-16 13:32:58,273 [INFO] main - 🔄 Kullanıcı bulunamadı, scraper tekrar başlatılıyor (main.py:182)
2025-06-16 13:33:00,140 [WARNING] urllib3.connectionpool - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000203826435E0>: Failed to establish a new connection: [WinError 10061] Hedef makine etkin olarak reddettiğinden bağlantı kurulamadı')': /session/bf10fbdf79a4464ebc28d34b3699c70f/url (connectionpool.py:827)
2025-06-16 13:33:08,474 [WARNING] urllib3.connectionpool - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000203826435B0>: Failed to establish a new connection: [WinError 10061] Hedef makine etkin olarak reddettiğinden bağlantı kurulamadı')': /session/bf10fbdf79a4464ebc28d34b3699c70f/actions (connectionpool.py:827)
2025-06-16 13:33:12,176 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 13:33:12,177 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 13:33:12,177 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:148)
2025-06-16 13:33:12,178 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:33:12,486 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 13:33:12,564 [WARNING] urllib3.connectionpool - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x00000203826423E0>: Failed to establish a new connection: [WinError 10061] Hedef makine etkin olarak reddettiğinden bağlantı kurulamadı')': /session/bf10fbdf79a4464ebc28d34b3699c70f/actions (connectionpool.py:827)
2025-06-16 13:33:26,030 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 13:33:26,030 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 13:33:26,030 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:148)
2025-06-16 13:33:26,030 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:33:26,248 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 13:33:28,233 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 13:33:28,233 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 13:33:28,248 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 13:33:28,264 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 13:33:28,264 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 13:33:38,311 [INFO] __main__ - 📨 Komut: start (ID: 355) (automation_worker.py:214)
2025-06-16 13:33:38,998 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 13:33:42,170 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 13:33:42,170 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 13:33:42,170 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 13:33:42,170 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 13:33:42,170 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 13:33:42,170 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 13:33:42,170 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:234)
2025-06-16 13:33:42,170 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:333)
2025-06-16 13:33:42,170 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 355) (automation_worker.py:150)
2025-06-16 13:33:44,436 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:333)
2025-06-16 13:33:47,376 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:333)
2025-06-16 13:33:47,377 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:333)
2025-06-16 13:33:54,467 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:333)
2025-06-16 13:34:00,415 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 438 (scraper_thread.py:333)
2025-06-16 13:34:09,295 [INFO] scraper_thread - [SCRAPER] aboofatos | 14 (scraper_thread.py:333)
2025-06-16 13:34:13,904 [INFO] scraper_thread - [SCRAPER] erkangk01 | 149 (scraper_thread.py:333)
2025-06-16 13:34:19,702 [INFO] scraper_thread - [SCRAPER] tuncay.ahi65 | 252 (scraper_thread.py:333)
2025-06-16 13:34:25,324 [INFO] scraper_thread - [SCRAPER] ebocuk | 11 (scraper_thread.py:333)
2025-06-16 13:34:30,780 [INFO] scraper_thread - [SCRAPER] taylancelik00 | 7 (scraper_thread.py:333)
2025-06-16 13:34:35,349 [INFO] scraper_thread - [SCRAPER] havvanrerel | 31 (scraper_thread.py:333)
2025-06-16 13:34:39,280 [INFO] scraper_thread - [SCRAPER] vedatbatmaz8 | 0 (scraper_thread.py:333)
2025-06-16 13:34:44,410 [INFO] scraper_thread - [SCRAPER] halilik34 | 0 (scraper_thread.py:333)
2025-06-16 13:34:48,584 [INFO] scraper_thread - [SCRAPER] nurcanyazarr | 6 (scraper_thread.py:333)
2025-06-16 13:34:53,064 [INFO] scraper_thread - [SCRAPER] hozan.cetin73 | 34 (scraper_thread.py:333)
2025-06-16 13:34:58,986 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 11 benzersiz kullanıcı bulundu (scraper_thread.py:333)
2025-06-16 13:35:01,577 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome driver kapatıldı (scraper_thread.py:333)
2025-06-16 13:35:01,717 [INFO] scraper_thread - [SCRAPER] 🔄 Tüm Chrome işlemleri kapatıldı (scraper_thread.py:333)
2025-06-16 13:35:07,045 [INFO] main - 🏁 scraper tamamlandı (main.py:113)
2025-06-16 13:35:07,045 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:35:10,202 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 13:35:13,244 [INFO] main - 2️⃣ Status Checker başlatılıyor (9 kullanıcı) (main.py:179)
2025-06-16 13:37:54,052 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 13:37:54,052 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 13:37:54,065 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 13:37:54,110 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 13:37:54,112 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 13:37:58,201 [INFO] __main__ - 📨 Komut: start (ID: 356) (automation_worker.py:214)
2025-06-16 13:37:58,305 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 13:37:58,830 [INFO] __main__ - 📨 Komut: start (ID: 356) (automation_worker.py:214)
2025-06-16 13:37:58,831 [INFO] __main__ - ⚠️ Otomasyon zaten çalışıyor (automation_worker.py:119)
2025-06-16 13:38:01,134 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 13:38:01,479 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 13:38:01,803 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 13:38:01,821 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 13:38:01,900 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 13:38:02,028 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 13:38:02,238 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 13:38:02,304 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 13:38:02,352 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 13:38:02,352 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:289)
2025-06-16 13:38:02,448 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 356) (automation_worker.py:150)
2025-06-16 13:38:02,506 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:333)
2025-06-16 13:38:05,118 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:333)
2025-06-16 13:38:10,231 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:333)
2025-06-16 13:38:10,232 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:333)
2025-06-16 13:38:32,049 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:333)
2025-06-16 13:38:36,623 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 536 (scraper_thread.py:333)
2025-06-16 13:38:46,341 [INFO] scraper_thread - [SCRAPER] sekofitness | 264 (scraper_thread.py:333)
2025-06-16 13:38:51,412 [INFO] scraper_thread - [SCRAPER] ozgenizresmii | 31 (scraper_thread.py:333)
2025-06-16 13:38:55,633 [INFO] scraper_thread - [SCRAPER] selimay051 | 5 (scraper_thread.py:333)
2025-06-16 13:39:00,361 [INFO] scraper_thread - [SCRAPER] eceshaw | 78 (scraper_thread.py:333)
2025-06-16 13:39:06,148 [INFO] scraper_thread - [SCRAPER] alierenkoyuncu | 1 (scraper_thread.py:333)
2025-06-16 13:39:11,418 [INFO] scraper_thread - [SCRAPER] ibrahim1.234 | 1 (scraper_thread.py:333)
2025-06-16 13:39:15,647 [INFO] scraper_thread - [SCRAPER] f4mariaf4 | 10 (scraper_thread.py:333)
2025-06-16 13:39:19,998 [INFO] scraper_thread - [SCRAPER] karaca4816 | 59 (scraper_thread.py:333)
2025-06-16 13:39:25,207 [INFO] scraper_thread - [SCRAPER] simaybnimkarim | 25 (scraper_thread.py:333)
2025-06-16 13:39:30,202 [INFO] scraper_thread - [SCRAPER] pmsekerci | 26 (scraper_thread.py:333)
2025-06-16 13:39:34,438 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 11 benzersiz kullanıcı bulundu (scraper_thread.py:333)
2025-06-16 13:39:37,358 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome driver kapatıldı (scraper_thread.py:333)
2025-06-16 13:39:37,569 [INFO] scraper_thread - [SCRAPER] 🔄 Tüm Chrome işlemleri kapatıldı (scraper_thread.py:333)
2025-06-16 13:39:42,235 [INFO] main - 🏁 scraper tamamlandı (main.py:118)
2025-06-16 13:39:42,235 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:39:45,421 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 13:39:48,438 [INFO] main - ⚠️ Status Checker geçici olarak devre dışı - 9 kullanıcı 'Uygun' olarak işaretleniyor (main.py:217)
2025-06-16 13:39:48,452 [INFO] main - ✅ 9 kullanıcı 'Uygun' olarak güncellendi (main.py:228)
2025-06-16 13:39:48,488 [INFO] main - 3️⃣ Message Sender başlatılıyor (6 kullanıcı) (main.py:259)
2025-06-16 13:42:56,106 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 13:42:56,107 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 13:42:56,108 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:184)
2025-06-16 13:42:56,108 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:42:56,848 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 13:43:16,827 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 13:43:16,827 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 13:43:16,827 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 13:43:16,842 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 13:43:16,842 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 13:45:11,545 [INFO] __main__ - 📨 Komut: start (ID: 357) (automation_worker.py:214)
2025-06-16 13:45:12,686 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 13:45:15,889 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 13:45:15,889 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 13:45:15,890 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 13:45:15,890 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 13:45:15,890 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 13:45:15,890 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 13:45:15,890 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:270)
2025-06-16 13:45:15,890 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:333)
2025-06-16 13:45:15,890 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 357) (automation_worker.py:150)
2025-06-16 13:45:18,061 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:333)
2025-06-16 13:45:20,454 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:333)
2025-06-16 13:45:20,455 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:333)
2025-06-16 13:45:28,221 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:333)
2025-06-16 13:45:33,737 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 390 (scraper_thread.py:333)
2025-06-16 13:45:51,253 [INFO] scraper_thread - [SCRAPER] tunahanerdemm | 90 (scraper_thread.py:333)
2025-06-16 13:46:00,363 [INFO] scraper_thread - [SCRAPER] yarensahinq | 19 (scraper_thread.py:333)
2025-06-16 13:46:04,297 [INFO] scraper_thread - [SCRAPER] selimay051 | 6 (scraper_thread.py:333)
2025-06-16 13:46:08,020 [INFO] scraper_thread - [SCRAPER] hozan.cetin73 | 30 (scraper_thread.py:333)
2025-06-16 13:46:13,653 [INFO] scraper_thread - [SCRAPER] blackdiamondorjj | 11 (scraper_thread.py:333)
2025-06-16 13:46:17,738 [INFO] scraper_thread - [SCRAPER] eceshaw | 60 (scraper_thread.py:333)
2025-06-16 13:46:21,977 [INFO] scraper_thread - [SCRAPER] fuatustaa | 19 (scraper_thread.py:333)
2025-06-16 13:46:27,429 [INFO] scraper_thread - [SCRAPER] usering4333800.0 | 41 (scraper_thread.py:333)
2025-06-16 13:46:33,065 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 9 benzersiz kullanıcı bulundu (scraper_thread.py:333)
2025-06-16 13:46:35,827 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome driver kapatıldı (scraper_thread.py:333)
2025-06-16 13:46:35,967 [INFO] scraper_thread - [SCRAPER] 🔄 Tüm Chrome işlemleri kapatıldı (scraper_thread.py:333)
2025-06-16 13:46:41,483 [INFO] main - 🏁 scraper tamamlandı (main.py:118)
2025-06-16 13:46:41,483 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:46:44,639 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 13:46:47,655 [INFO] main - 2️⃣ Status Checker başlatılıyor (8 kullanıcı) (main.py:215)
2025-06-16 13:58:33,010 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 13:58:33,011 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 13:58:33,012 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:184)
2025-06-16 13:58:33,012 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 13:58:36,192 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 13:59:48,651 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 13:59:48,652 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 13:59:48,664 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 13:59:48,678 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 13:59:48,678 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:00:23,881 [INFO] __main__ - 📨 Komut: start (ID: 358) (automation_worker.py:214)
2025-06-16 14:00:23,986 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:00:27,252 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:00:27,253 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 14:00:27,253 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 14:00:27,256 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 14:00:27,256 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 14:00:27,257 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 14:00:27,257 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:270)
2025-06-16 14:00:27,258 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:333)
2025-06-16 14:00:27,262 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 358) (automation_worker.py:150)
2025-06-16 14:00:29,491 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:333)
2025-06-16 14:00:34,736 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:333)
2025-06-16 14:00:34,737 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:333)
2025-06-16 14:00:37,726 [ERROR] scraper_thread - [SCRAPER] Scraper döngüsünde hata: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: Unable to receive message from renderer
  (Session info: chrome=137.0.7151.68)
Stacktrace:
	GetHandleVerifier [0x0x7ff6555dfea5+79173]
	GetHandleVerifier [0x0x7ff6555dff00+79264]
	(No symbol) [0x0x7ff655399e5a]
	(No symbol) [0x0x7ff6553872dc]
	(No symbol) [0x0x7ff655386fca]
	(No symbol) [0x0x7ff655384b9f]
	(No symbol) [0x0x7ff6553855ff]
	(No symbol) [0x0x7ff6553942ae]
	(No symbol) [0x0x7ff6553aa671]
	(No symbol) [0x0x7ff6553b17ba]
	(No symbol) [0x0x7ff655385d9d]
	(No symbol) [0x0x7ff6553a9e61]
	(No symbol) [0x0x7ff655441384]
	(No symbol) [0x0x7ff655418743]
	(No symbol) [0x0x7ff6553e14c1]
	(No symbol) [0x0x7ff6553e2253]
	GetHandleVerifier [0x0x7ff6558aa2dd+3004797]
	GetHandleVerifier [0x0x7ff6558a472d+2981325]
	GetHandleVerifier [0x0x7ff6558c3380+3107360]
	GetHandleVerifier [0x0x7ff6555faa2e+188622]
	GetHandleVerifier [0x0x7ff6556022bf+219487]
	GetHandleVerifier [0x0x7ff6555e8df4+115860]
	GetHandleVerifier [0x0x7ff6555e8fa9+116297]
	GetHandleVerifier [0x0x7ff6555cf558+11256]
	BaseThreadInitThunk [0x0x7ff80e937374+20]
	RtlUserThreadStart [0x0x7ff81081cc91+33]
 (scraper_thread.py:337)
2025-06-16 14:00:37,731 [WARNING] scraper_thread - [SCRAPER] Chrome session kaybedildi, yeniden başlatma deneniyor... (scraper_thread.py:335)
2025-06-16 14:00:59,308 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:333)
2025-06-16 14:00:59,363 [INFO] scraper_thread - [SCRAPER] Chrome yeniden başlatıldı (scraper_thread.py:333)
2025-06-16 14:01:08,918 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:01:09,008 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:01:09,051 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:01:09,077 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:01:09,165 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:01:25,280 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 0 benzersiz kullanıcı bulundu (scraper_thread.py:333)
2025-06-16 14:01:28,077 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome driver kapatıldı (scraper_thread.py:333)
2025-06-16 14:01:28,243 [INFO] scraper_thread - [SCRAPER] 🔄 Tüm Chrome işlemleri kapatıldı (scraper_thread.py:333)
2025-06-16 14:01:33,533 [INFO] main - 🏁 scraper tamamlandı (main.py:118)
2025-06-16 14:01:33,533 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 14:01:36,708 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 14:01:39,717 [INFO] main - 🔄 Kullanıcı bulunamadı, scraper tekrar başlatılıyor (main.py:218)
2025-06-16 14:02:05,929 [INFO] __main__ - 📨 Komut: start (ID: 359) (automation_worker.py:214)
2025-06-16 14:02:05,983 [INFO] __main__ - ⚠️ Otomasyon zaten çalışıyor (automation_worker.py:119)
2025-06-16 14:02:23,829 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:02:23,830 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:02:23,840 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:02:23,861 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:02:23,861 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:04:03,130 [INFO] __main__ - ✅ Import'lar başarılı (test_status_checker.py:25)
2025-06-16 14:04:03,131 [INFO] __main__ - ✅ DatabaseManager oluşturuldu (test_status_checker.py:29)
2025-06-16 14:04:03,131 [INFO] __main__ - 🔧 StatusCheckerThread oluşturuluyor... (2 kullanıcı) (test_status_checker.py:37)
2025-06-16 14:04:03,132 [INFO] __main__ - ✅ StatusCheckerThread oluşturuldu (test_status_checker.py:46)
2025-06-16 14:04:03,132 [INFO] __main__ - 🚀 StatusCheckerThread başlatılıyor... (test_status_checker.py:47)
2025-06-16 14:04:03,132 [INFO] __main__ - ✅ StatusCheckerThread başlatıldı (test_status_checker.py:51)
2025-06-16 14:04:03,132 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread başlatıldı (status_checker.py:85)
2025-06-16 14:04:03,133 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 2 (status_checker.py:85)
2025-06-16 14:04:03,133 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome işlemleri temizleniyor... (status_checker.py:85)
2025-06-16 14:04:08,202 [INFO] __main__ - ✅ Test tamamlandı (test_status_checker.py:57)
2025-06-16 14:04:20,231 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:04:20,233 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:04:20,245 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:04:20,267 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:04:20,268 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:04:52,099 [INFO] __main__ - 📨 Komut: start (ID: 360) (automation_worker.py:214)
2025-06-16 14:04:52,100 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:04:52,473 [INFO] __main__ - 📨 Komut: start (ID: 360) (automation_worker.py:214)
2025-06-16 14:04:52,475 [INFO] __main__ - ⚠️ Otomasyon zaten çalışıyor (automation_worker.py:119)
2025-06-16 14:04:55,283 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:04:55,283 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 14:04:55,283 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 14:04:55,286 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 14:04:55,287 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 14:04:55,287 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 14:04:55,287 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:270)
2025-06-16 14:04:55,288 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:333)
2025-06-16 14:04:55,291 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 360) (automation_worker.py:150)
2025-06-16 14:04:57,465 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:333)
2025-06-16 14:05:03,851 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:333)
2025-06-16 14:05:03,853 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:333)
2025-06-16 14:05:17,925 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:333)
2025-06-16 14:05:22,809 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 422 (scraper_thread.py:333)
2025-06-16 14:05:32,558 [INFO] scraper_thread - [SCRAPER] nazzlnurr | 64 (scraper_thread.py:333)
2025-06-16 14:05:37,398 [INFO] scraper_thread - [SCRAPER] elinags1905 | 14 (scraper_thread.py:333)
2025-06-16 14:05:43,423 [INFO] scraper_thread - [SCRAPER] clcgunes_ | 31 (scraper_thread.py:333)
2025-06-16 14:05:48,467 [INFO] scraper_thread - [SCRAPER] __mehmet___7 | 224 (scraper_thread.py:333)
2025-06-16 14:05:54,838 [INFO] scraper_thread - [SCRAPER] baharkupsi_ | 20 (scraper_thread.py:333)
2025-06-16 14:06:00,667 [INFO] scraper_thread - [SCRAPER] halimeyy14 | 7 (scraper_thread.py:333)
2025-06-16 14:06:23,858 [INFO] main - ✅ Otomasyon durduruldu (main.py:196)
2025-06-16 14:06:25,860 [INFO] __main__ - ✅ Otomasyon temizlendi (automation_worker.py:97)
2025-06-16 14:06:38,546 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:06:41,874 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:06:41,949 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 14:07:00,702 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:07:00,702 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:07:00,717 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:07:00,733 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:07:00,733 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:07:12,811 [INFO] __main__ - 📨 Komut: start (ID: 361) (automation_worker.py:214)
2025-06-16 14:07:14,561 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:07:17,717 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:07:17,717 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 14:07:17,717 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 14:07:17,717 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 14:07:17,717 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 14:07:17,717 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 14:07:17,717 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:270)
2025-06-16 14:07:17,717 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:333)
2025-06-16 14:07:17,717 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 361) (automation_worker.py:150)
2025-06-16 14:07:19,896 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:333)
2025-06-16 14:07:46,962 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:333)
2025-06-16 14:07:46,963 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:333)
2025-06-16 14:07:54,655 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:333)
2025-06-16 14:07:58,994 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 390 (scraper_thread.py:333)
2025-06-16 14:08:07,873 [INFO] scraper_thread - [SCRAPER] sinem6534 | 306 (scraper_thread.py:333)
2025-06-16 14:08:13,756 [INFO] scraper_thread - [SCRAPER] tuncay.ahi65 | 342 (scraper_thread.py:333)
2025-06-16 14:08:17,730 [INFO] scraper_thread - [SCRAPER] veliyldrm_42 | 7 (scraper_thread.py:333)
2025-06-16 14:08:22,892 [INFO] scraper_thread - [SCRAPER] halilik34 | 0 (scraper_thread.py:333)
2025-06-16 14:08:27,185 [INFO] scraper_thread - [SCRAPER] a_mer_.99 | 6 (scraper_thread.py:333)
2025-06-16 14:08:32,574 [INFO] scraper_thread - [SCRAPER] cerkeshostes | 9 (scraper_thread.py:333)
2025-06-16 14:08:36,801 [INFO] scraper_thread - [SCRAPER] ertanisbilirmusic | 11 (scraper_thread.py:333)
2025-06-16 14:08:40,959 [INFO] scraper_thread - [SCRAPER] vedatbatmaz8 | 1 (scraper_thread.py:333)
2025-06-16 14:08:45,731 [INFO] scraper_thread - [SCRAPER] emirhan.turhan6 | 0 (scraper_thread.py:333)
2025-06-16 14:08:49,890 [INFO] scraper_thread - [SCRAPER] f4mariaf4 | 7 (scraper_thread.py:333)
2025-06-16 14:08:55,272 [INFO] scraper_thread - [SCRAPER] emirhannq1 | 1 (scraper_thread.py:333)
2025-06-16 14:08:55,274 [INFO] scraper_thread - [SCRAPER] Süre doldu (1.0 dk) (scraper_thread.py:333)
2025-06-16 14:08:55,274 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 12 benzersiz kullanıcı bulundu (scraper_thread.py:333)
2025-06-16 14:08:57,873 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome driver kapatıldı (scraper_thread.py:333)
2025-06-16 14:08:58,014 [INFO] scraper_thread - [SCRAPER] 🔄 Tüm Chrome işlemleri kapatıldı (scraper_thread.py:333)
2025-06-16 14:09:02,545 [INFO] main - 🏁 scraper tamamlandı (main.py:118)
2025-06-16 14:09:02,545 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 14:09:05,702 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 14:09:08,717 [INFO] main - 2️⃣ Status Checker başlatılıyor (10 kullanıcı) (main.py:215)
2025-06-16 14:13:02,056 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:13:02,059 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:13:02,108 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:13:02,122 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:13:02,122 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:13:02,346 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 14:13:02,346 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 14:13:02,347 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:184)
2025-06-16 14:13:02,347 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 14:13:02,543 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 14:13:18,627 [INFO] __main__ - 📨 Komut: start (ID: 362) (automation_worker.py:214)
2025-06-16 14:13:18,722 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:13:23,121 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:13:23,121 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 14:13:23,122 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 14:13:23,125 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 14:13:23,125 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 14:13:23,125 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 14:13:23,128 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:270)
2025-06-16 14:13:23,128 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:322)
2025-06-16 14:13:23,132 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 362) (automation_worker.py:150)
2025-06-16 14:13:25,538 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:322)
2025-06-16 14:13:33,960 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:322)
2025-06-16 14:13:33,961 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:322)
2025-06-16 14:13:44,532 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:322)
2025-06-16 14:13:50,396 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 326 (scraper_thread.py:322)
2025-06-16 14:13:56,141 [INFO] scraper_thread - [SCRAPER] __mehmet___7 | 147 (scraper_thread.py:322)
2025-06-16 14:14:01,622 [INFO] scraper_thread - [SCRAPER] sinem6534 | 303 (scraper_thread.py:322)
2025-06-16 14:14:10,742 [INFO] scraper_thread - [SCRAPER] yaso_ln | 34 (scraper_thread.py:322)
2025-06-16 14:14:28,481 [INFO] scraper_thread - [SCRAPER] xevasee | 6 (scraper_thread.py:322)
2025-06-16 14:14:32,657 [INFO] scraper_thread - [SCRAPER] keniverse0 | 7 (scraper_thread.py:322)
2025-06-16 14:14:38,079 [INFO] scraper_thread - [SCRAPER] user4569951875051 | 16 (scraper_thread.py:322)
2025-06-16 14:14:43,742 [INFO] scraper_thread - [SCRAPER] kntwestron | 54 (scraper_thread.py:322)
2025-06-16 14:14:48,173 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 8 benzersiz kullanıcı bulundu (scraper_thread.py:322)
2025-06-16 14:14:51,421 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:322)
2025-06-16 14:14:55,827 [INFO] main - 🏁 scraper tamamlandı (main.py:118)
2025-06-16 14:14:55,827 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 14:14:59,062 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 14:15:02,078 [INFO] main - 2️⃣ Status Checker başlatılıyor (5 kullanıcı) (main.py:215)
2025-06-16 14:16:24,439 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:16:24,440 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:16:24,449 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:16:24,460 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:16:24,461 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:16:38,603 [INFO] __main__ - 📨 Komut: start (ID: 363) (automation_worker.py:214)
2025-06-16 14:16:38,603 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:16:41,775 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:16:41,775 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 14:16:41,776 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 14:16:41,778 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 14:16:41,779 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 14:16:41,779 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 14:16:41,779 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:270)
2025-06-16 14:16:41,781 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:322)
2025-06-16 14:16:41,784 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 363) (automation_worker.py:150)
2025-06-16 14:16:44,141 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:322)
2025-06-16 14:16:48,977 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:322)
2025-06-16 14:16:48,977 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:322)
2025-06-16 14:16:58,084 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:322)
2025-06-16 14:17:02,669 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 417 (scraper_thread.py:322)
2025-06-16 14:17:23,146 [INFO] scraper_thread - [SCRAPER] yedekhesap132 | 61 (scraper_thread.py:322)
2025-06-16 14:17:28,464 [INFO] scraper_thread - [SCRAPER] ibrahim1.234 | 4 (scraper_thread.py:322)
2025-06-16 14:17:34,027 [INFO] scraper_thread - [SCRAPER] arterpubg | 11 (scraper_thread.py:322)
2025-06-16 14:17:38,294 [INFO] scraper_thread - [SCRAPER] ich.turk | 115 (scraper_thread.py:322)
2025-06-16 14:17:44,997 [INFO] scraper_thread - [SCRAPER] qeeasy | 78 (scraper_thread.py:322)
2025-06-16 14:17:50,867 [INFO] scraper_thread - [SCRAPER] hulusi.ozcnn | 4 (scraper_thread.py:322)
2025-06-16 14:17:56,515 [INFO] scraper_thread - [SCRAPER] antepli.63 | 15 (scraper_thread.py:322)
2025-06-16 14:18:03,247 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 8 benzersiz kullanıcı bulundu (scraper_thread.py:322)
2025-06-16 14:18:08,283 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:322)
2025-06-16 14:18:13,305 [INFO] main - 🏁 scraper tamamlandı (main.py:118)
2025-06-16 14:18:13,307 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 14:18:16,768 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 14:18:19,784 [INFO] main - 2️⃣ Status Checker başlatılıyor (7 kullanıcı) (main.py:215)
2025-06-16 14:19:50,301 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 14:19:50,301 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 14:19:50,302 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:184)
2025-06-16 14:19:50,302 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 14:19:53,602 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 14:19:53,641 [INFO] main - ✅ Otomasyon durduruldu (main.py:196)
2025-06-16 14:19:55,698 [INFO] __main__ - ✅ Otomasyon temizlendi (automation_worker.py:97)
2025-06-16 14:19:55,700 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:19:59,754 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:19:59,755 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 14:22:15,119 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:22:15,120 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:22:15,133 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:22:15,147 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:22:15,149 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:22:31,615 [INFO] __main__ - 📨 Komut: start (ID: 364) (automation_worker.py:214)
2025-06-16 14:22:31,616 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:22:35,169 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:22:35,174 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 14:22:35,174 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 14:22:35,177 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 14:22:35,177 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 14:22:35,178 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 14:22:35,178 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:279)
2025-06-16 14:22:35,182 [INFO] scraper_thread - [SCRAPER] 🔄 Chrome işlemleri temizleniyor... (scraper_thread.py:322)
2025-06-16 14:22:35,183 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 364) (automation_worker.py:150)
2025-06-16 14:22:37,760 [INFO] scraper_thread - [SCRAPER] ✅ Chrome işlemleri temizlendi (scraper_thread.py:322)
2025-06-16 14:22:48,081 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:322)
2025-06-16 14:22:48,081 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:322)
2025-06-16 14:22:57,645 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:322)
2025-06-16 14:23:07,123 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 358 (scraper_thread.py:322)
2025-06-16 14:23:16,522 [INFO] scraper_thread - [SCRAPER] tuncay.ahi65 | 304 (scraper_thread.py:322)
2025-06-16 14:23:21,278 [INFO] scraper_thread - [SCRAPER] delviagoyeni | 8 (scraper_thread.py:322)
2025-06-16 14:23:29,196 [INFO] scraper_thread - [SCRAPER] gkhn6012 | 27 (scraper_thread.py:322)
2025-06-16 14:23:34,271 [INFO] scraper_thread - [SCRAPER] usering4333800.0 | 20 (scraper_thread.py:322)
2025-06-16 14:23:41,088 [INFO] scraper_thread - [SCRAPER] furkan_onur34 | 4 (scraper_thread.py:322)
2025-06-16 14:23:47,964 [INFO] scraper_thread - [SCRAPER] miraculuskurdish | 26 (scraper_thread.py:322)
2025-06-16 14:23:54,439 [INFO] scraper_thread - [SCRAPER] ebocuk | 17 (scraper_thread.py:322)
2025-06-16 14:23:59,286 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 8 benzersiz kullanıcı bulundu (scraper_thread.py:322)
2025-06-16 14:24:01,968 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:322)
2025-06-16 14:24:07,843 [INFO] main - 🏁 scraper tamamlandı (main.py:127)
2025-06-16 14:24:07,843 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 14:24:11,031 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 14:24:14,046 [INFO] main - 2️⃣ Status Checker başlatılıyor (7 kullanıcı) (main.py:224)
2025-06-16 14:24:36,644 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 14:24:36,645 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 14:24:36,645 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:193)
2025-06-16 14:24:36,645 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 14:24:39,812 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 14:24:39,813 [INFO] main - ✅ Otomasyon durduruldu (main.py:205)
2025-06-16 14:24:41,826 [INFO] __main__ - ✅ Otomasyon temizlendi (automation_worker.py:97)
2025-06-16 14:24:41,826 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:24:44,110 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 14:30:35,768 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:30:35,769 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:30:35,781 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:30:35,799 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:30:35,799 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:30:46,889 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 14:30:46,905 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:30:47,139 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 14:31:08,092 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:31:08,092 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:31:08,108 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:31:08,123 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:31:08,123 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:31:16,139 [INFO] __main__ - 📨 Komut: start (ID: 365) (automation_worker.py:214)
2025-06-16 14:32:09,452 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:32:12,639 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:32:12,639 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 14:32:12,640 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 14:32:12,640 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 14:32:12,640 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 14:32:12,640 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 14:32:12,640 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:270)
2025-06-16 14:32:12,640 [INFO] main - ✅ Scraper thread başlatıldı (main.py:283)
2025-06-16 14:32:12,640 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 365) (automation_worker.py:150)
2025-06-16 14:32:17,027 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:313)
2025-06-16 14:32:17,028 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:313)
2025-06-16 14:32:24,733 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:313)
2025-06-16 14:32:31,892 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 270 (scraper_thread.py:313)
2025-06-16 14:32:37,333 [INFO] scraper_thread - [SCRAPER] theangarabebesi | 11 (scraper_thread.py:313)
2025-06-16 14:32:41,205 [INFO] scraper_thread - [SCRAPER] sinem6534 | 791 (scraper_thread.py:313)
2025-06-16 14:32:45,677 [INFO] scraper_thread - [SCRAPER] caglardgnn | 16 (scraper_thread.py:313)
2025-06-16 14:33:35,217 [INFO] scraper_thread - [SCRAPER] Süre doldu (1.0 dk) (scraper_thread.py:313)
2025-06-16 14:33:35,218 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 4 benzersiz kullanıcı bulundu (scraper_thread.py:313)
2025-06-16 14:33:37,764 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:313)
2025-06-16 14:33:43,233 [INFO] main - 🏁 scraper tamamlandı (main.py:118)
2025-06-16 14:33:45,467 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 14:33:48,655 [INFO] main - ✅ Chrome kapatıldı (main.py:73)
2025-06-16 14:33:51,670 [INFO] main - 2️⃣ Status Checker başlatılıyor (1 kullanıcı) (main.py:215)
2025-06-16 14:35:53,471 [INFO] __main__ - ✅ Import'lar başarılı (test_status_simple.py:28)
2025-06-16 14:35:53,471 [INFO] __main__ - ✅ DatabaseManager oluşturuldu (test_status_simple.py:32)
2025-06-16 14:35:53,472 [INFO] __main__ - 🔧 StatusCheckerThread oluşturuluyor... (2 kullanıcı) (test_status_simple.py:40)
2025-06-16 14:35:53,472 [INFO] __main__ - ✅ StatusCheckerThread oluşturuldu (test_status_simple.py:49)
2025-06-16 14:35:53,472 [INFO] __main__ - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (test_status_simple.py:50)
2025-06-16 14:35:53,473 [INFO] __main__ - 🔧 Thread daemon: True (test_status_simple.py:51)
2025-06-16 14:35:53,473 [INFO] __main__ - 🚀 StatusCheckerThread başlatılıyor... (test_status_simple.py:53)
2025-06-16 14:35:53,474 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:82)
2025-06-16 14:35:53,474 [INFO] __main__ - ✅ StatusCheckerThread.start() çağrıldı (test_status_simple.py:57)
2025-06-16 14:35:53,474 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 2 (status_checker.py:82)
2025-06-16 14:35:53,474 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 3152 (status_checker.py:82)
2025-06-16 14:35:53,474 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:82)
2025-06-16 14:35:53,475 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Try bloğuna girdi (status_checker.py:82)
2025-06-16 14:35:53,475 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:82)
2025-06-16 14:35:55,480 [INFO] __main__ - 🔧 Thread alive: True (test_status_simple.py:61)
2025-06-16 14:35:55,480 [INFO] __main__ - ⏳ Thread hala çalışıyor... (1/10) (test_status_simple.py:66)
2025-06-16 14:35:56,494 [INFO] __main__ - ⏳ Thread hala çalışıyor... (2/10) (test_status_simple.py:66)
2025-06-16 14:35:56,800 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:82)
2025-06-16 14:35:56,800 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 2 kullanıcı işlenecek (status_checker.py:82)
2025-06-16 14:35:56,801 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'test_user1'}, {'username': 'test_user2'}] (status_checker.py:82)
2025-06-16 14:35:56,801 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:82)
2025-06-16 14:35:56,801 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:82)
2025-06-16 14:35:56,801 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:82)
2025-06-16 14:35:56,801 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:82)
2025-06-16 14:35:56,802 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:82)
2025-06-16 14:35:56,802 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:82)
2025-06-16 14:35:56,802 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:82)
2025-06-16 14:35:56,803 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:82)
2025-06-16 14:35:57,503 [INFO] __main__ - ⏳ Thread hala çalışıyor... (3/10) (test_status_simple.py:66)
2025-06-16 14:35:58,685 [INFO] __main__ - ⏳ Thread hala çalışıyor... (4/10) (test_status_simple.py:66)
2025-06-16 14:36:00,435 [INFO] __main__ - ⏳ Thread hala çalışıyor... (5/10) (test_status_simple.py:66)
2025-06-16 14:36:01,861 [INFO] __main__ - ⏳ Thread hala çalışıyor... (6/10) (test_status_simple.py:66)
2025-06-16 14:36:01,886 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:82)
2025-06-16 14:36:02,890 [INFO] __main__ - ⏳ Thread hala çalışıyor... (7/10) (test_status_simple.py:66)
2025-06-16 14:36:04,264 [INFO] __main__ - ⏳ Thread hala çalışıyor... (8/10) (test_status_simple.py:66)
2025-06-16 14:36:06,012 [INFO] __main__ - ⏳ Thread hala çalışıyor... (9/10) (test_status_simple.py:66)
2025-06-16 14:36:06,497 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:82)
2025-06-16 14:36:06,498 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:82)
2025-06-16 14:36:06,498 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:82)
2025-06-16 14:36:06,504 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:82)
2025-06-16 14:36:07,280 [INFO] __main__ - ⏳ Thread hala çalışıyor... (10/10) (test_status_simple.py:66)
2025-06-16 14:36:08,553 [INFO] __main__ - ✅ Test tamamlandı (test_status_simple.py:72)
2025-06-16 14:36:08,931 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 14:36:08,931 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 14:36:09,241 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:184)
2025-06-16 14:36:09,436 [INFO] main - 🔄 Chrome kapatılıyor... (main.py:64)
2025-06-16 14:36:09,560 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 14:37:47,274 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:37:47,275 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:37:47,291 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:37:47,307 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:37:47,307 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:38:21,639 [INFO] __main__ - ✅ Import'lar başarılı (test_status_checker.py:25)
2025-06-16 14:38:21,639 [INFO] __main__ - ✅ DatabaseManager oluşturuldu (test_status_checker.py:29)
2025-06-16 14:38:21,639 [INFO] __main__ - 🔧 StatusCheckerThread oluşturuluyor... (2 kullanıcı) (test_status_checker.py:37)
2025-06-16 14:38:21,639 [INFO] __main__ - ✅ StatusCheckerThread oluşturuldu (test_status_checker.py:46)
2025-06-16 14:38:21,639 [INFO] __main__ - 🚀 StatusCheckerThread başlatılıyor... (test_status_checker.py:47)
2025-06-16 14:38:21,639 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:82)
2025-06-16 14:38:21,639 [INFO] __main__ - ✅ StatusCheckerThread başlatıldı (test_status_checker.py:51)
2025-06-16 14:38:21,639 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 2 (status_checker.py:82)
2025-06-16 14:38:21,639 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 10812 (status_checker.py:82)
2025-06-16 14:38:21,639 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:82)
2025-06-16 14:38:21,639 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Try bloğuna girdi (status_checker.py:82)
2025-06-16 14:38:21,639 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:82)
2025-06-16 14:38:24,811 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:82)
2025-06-16 14:38:24,811 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 2 kullanıcı işlenecek (status_checker.py:82)
2025-06-16 14:38:24,811 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'test_user1'}, {'username': 'test_user2'}] (status_checker.py:82)
2025-06-16 14:38:24,811 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:82)
2025-06-16 14:38:24,811 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:82)
2025-06-16 14:38:24,811 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:82)
2025-06-16 14:38:24,811 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:82)
2025-06-16 14:38:24,811 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:82)
2025-06-16 14:38:24,811 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:82)
2025-06-16 14:38:24,811 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:82)
2025-06-16 14:38:24,811 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:82)
2025-06-16 14:38:26,428 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:82)
2025-06-16 14:38:26,642 [INFO] __main__ - ✅ Test tamamlandı (test_status_checker.py:57)
2025-06-16 14:40:34,823 [INFO] __main__ - ✅ Import'lar başarılı (test_status_simple.py:28)
2025-06-16 14:40:34,823 [INFO] __main__ - ✅ DatabaseManager oluşturuldu (test_status_simple.py:32)
2025-06-16 14:40:34,824 [INFO] __main__ - 🔧 StatusCheckerThread oluşturuluyor... (2 kullanıcı) (test_status_simple.py:40)
2025-06-16 14:40:34,824 [INFO] __main__ - ✅ StatusCheckerThread oluşturuldu (test_status_simple.py:49)
2025-06-16 14:40:34,824 [INFO] __main__ - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (test_status_simple.py:50)
2025-06-16 14:40:34,825 [INFO] __main__ - 🔧 Thread daemon: True (test_status_simple.py:51)
2025-06-16 14:40:34,825 [INFO] __main__ - 🚀 StatusCheckerThread başlatılıyor... (test_status_simple.py:53)
2025-06-16 14:40:34,826 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:82)
2025-06-16 14:40:34,826 [INFO] __main__ - ✅ StatusCheckerThread.start() çağrıldı (test_status_simple.py:57)
2025-06-16 14:40:34,826 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 2 (status_checker.py:82)
2025-06-16 14:40:34,826 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 2968 (status_checker.py:82)
2025-06-16 14:40:34,827 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:82)
2025-06-16 14:40:34,827 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Try bloğuna girdi (status_checker.py:82)
2025-06-16 14:40:34,827 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:82)
2025-06-16 14:40:36,863 [INFO] __main__ - 🔧 Thread alive: True (test_status_simple.py:61)
2025-06-16 14:40:36,868 [INFO] __main__ - ⏳ Thread hala çalışıyor... (1/10) (test_status_simple.py:66)
2025-06-16 14:40:37,877 [INFO] __main__ - ⏳ Thread hala çalışıyor... (2/10) (test_status_simple.py:66)
2025-06-16 14:40:38,072 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:82)
2025-06-16 14:40:38,072 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 2 kullanıcı işlenecek (status_checker.py:82)
2025-06-16 14:40:38,073 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'test_user1'}, {'username': 'test_user2'}] (status_checker.py:82)
2025-06-16 14:40:38,073 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:82)
2025-06-16 14:40:38,073 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:82)
2025-06-16 14:40:38,073 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:82)
2025-06-16 14:40:38,074 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:82)
2025-06-16 14:40:38,074 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:82)
2025-06-16 14:40:38,074 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:82)
2025-06-16 14:40:38,075 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:82)
2025-06-16 14:40:38,075 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:82)
2025-06-16 14:40:38,924 [INFO] __main__ - ⏳ Thread hala çalışıyor... (3/10) (test_status_simple.py:66)
2025-06-16 14:40:40,296 [INFO] __main__ - ⏳ Thread hala çalışıyor... (4/10) (test_status_simple.py:66)
2025-06-16 14:40:41,777 [INFO] __main__ - ⏳ Thread hala çalışıyor... (5/10) (test_status_simple.py:66)
2025-06-16 14:40:42,437 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:82)
2025-06-16 14:40:42,843 [INFO] __main__ - ⏳ Thread hala çalışıyor... (6/10) (test_status_simple.py:66)
2025-06-16 14:40:43,870 [INFO] __main__ - ⏳ Thread hala çalışıyor... (7/10) (test_status_simple.py:66)
2025-06-16 14:40:44,931 [INFO] __main__ - ⏳ Thread hala çalışıyor... (8/10) (test_status_simple.py:66)
2025-06-16 14:40:45,483 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:82)
2025-06-16 14:40:45,483 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:82)
2025-06-16 14:40:45,484 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:82)
2025-06-16 14:40:45,485 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:82)
2025-06-16 14:40:46,232 [INFO] __main__ - ⏳ Thread hala çalışıyor... (9/10) (test_status_simple.py:66)
2025-06-16 14:40:47,306 [INFO] __main__ - ⏳ Thread hala çalışıyor... (10/10) (test_status_simple.py:66)
2025-06-16 14:40:48,480 [INFO] __main__ - ✅ Test tamamlandı (test_status_simple.py:72)
2025-06-16 14:41:07,002 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:41:07,003 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:41:07,013 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:41:07,027 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:41:07,027 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:41:21,379 [INFO] __main__ - 📨 Komut: start (ID: 366) (automation_worker.py:214)
2025-06-16 14:41:21,380 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:41:21,963 [INFO] __main__ - 📨 Komut: start (ID: 366) (automation_worker.py:214)
2025-06-16 14:41:22,011 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:41:25,658 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:41:25,659 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 14:41:25,659 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 14:41:25,662 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 14:41:25,662 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 14:41:25,663 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 14:41:25,663 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:269)
2025-06-16 14:41:25,663 [INFO] main - ✅ Scraper thread başlatıldı (main.py:282)
2025-06-16 14:41:25,668 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 366) (automation_worker.py:150)
2025-06-16 14:41:25,737 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:41:25,738 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 14:41:25,739 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 14:41:25,743 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 14:41:25,743 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 14:41:25,744 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 14:41:25,744 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:269)
2025-06-16 14:41:25,745 [INFO] main - ✅ Scraper thread başlatıldı (main.py:282)
2025-06-16 14:41:25,749 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 366) (automation_worker.py:150)
2025-06-16 14:41:34,061 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:313)
2025-06-16 14:41:34,061 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:313)
2025-06-16 14:41:34,073 [WARNING] scraper_thread - [SCRAPER] ⚠️ Bot algılama önlemleri uygulanamadı: Message: javascript error: Cannot redefine property: webdriver
  (Session info: chrome=137.0.7151.68)
Stacktrace:
	GetHandleVerifier [0x0x7ff6555dfea5+79173]
	GetHandleVerifier [0x0x7ff6555dff00+79264]
	(No symbol) [0x0x7ff655399e5a]
	(No symbol) [0x0x7ff6553a184d]
	(No symbol) [0x0x7ff6553a4be1]
	(No symbol) [0x0x7ff6554422b4]
	(No symbol) [0x0x7ff65541896a]
	(No symbol) [0x0x7ff65544100d]
	(No symbol) [0x0x7ff655418743]
	(No symbol) [0x0x7ff6553e14c1]
	(No symbol) [0x0x7ff6553e2253]
	GetHandleVerifier [0x0x7ff6558aa2dd+3004797]
	GetHandleVerifier [0x0x7ff6558a472d+2981325]
	GetHandleVerifier [0x0x7ff6558c3380+3107360]
	GetHandleVerifier [0x0x7ff6555faa2e+188622]
	GetHandleVerifier [0x0x7ff6556022bf+219487]
	GetHandleVerifier [0x0x7ff6555e8df4+115860]
	GetHandleVerifier [0x0x7ff6555e8fa9+116297]
	GetHandleVerifier [0x0x7ff6555cf558+11256]
	BaseThreadInitThunk [0x0x7ff80e937374+20]
	RtlUserThreadStart [0x0x7ff81081cc91+33]
 (scraper_thread.py:315)
2025-06-16 14:41:34,093 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:313)
2025-06-16 14:41:43,832 [ERROR] scraper_thread - [SCRAPER] Scraper döngüsünde hata: Message: unknown error: cannot determine loading status
from target frame detached
  (Session info: chrome=137.0.7151.68)
Stacktrace:
	GetHandleVerifier [0x0x7ff6555dfea5+79173]
	GetHandleVerifier [0x0x7ff6555dff00+79264]
	(No symbol) [0x0x7ff655399c8c]
	(No symbol) [0x0x7ff655386f6b]
	(No symbol) [0x0x7ff655384b9f]
	(No symbol) [0x0x7ff6553855ff]
	(No symbol) [0x0x7ff655394b3d]
	(No symbol) [0x0x7ff6553aa671]
	(No symbol) [0x0x7ff6553b17ba]
	(No symbol) [0x0x7ff655385d9d]
	(No symbol) [0x0x7ff6553a9e61]
	(No symbol) [0x0x7ff655441384]
	(No symbol) [0x0x7ff655418743]
	(No symbol) [0x0x7ff6553e14c1]
	(No symbol) [0x0x7ff6553e2253]
	GetHandleVerifier [0x0x7ff6558aa2dd+3004797]
	GetHandleVerifier [0x0x7ff6558a472d+2981325]
	GetHandleVerifier [0x0x7ff6558c3380+3107360]
	GetHandleVerifier [0x0x7ff6555faa2e+188622]
	GetHandleVerifier [0x0x7ff6556022bf+219487]
	GetHandleVerifier [0x0x7ff6555e8df4+115860]
	GetHandleVerifier [0x0x7ff6555e8fa9+116297]
	GetHandleVerifier [0x0x7ff6555cf558+11256]
	BaseThreadInitThunk [0x0x7ff80e937374+20]
	RtlUserThreadStart [0x0x7ff81081cc91+33]
 (scraper_thread.py:317)
2025-06-16 14:41:44,044 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 0 benzersiz kullanıcı bulundu (scraper_thread.py:313)
2025-06-16 14:41:45,741 [ERROR] scraper_thread - [SCRAPER] Scraper döngüsünde hata: Message: target frame detached
  (failed to check if window was closed: disconnected: unable to send message to renderer)
  (Session info: chrome=137.0.7151.68)
Stacktrace:
	GetHandleVerifier [0x0x7ff6555dfea5+79173]
	GetHandleVerifier [0x0x7ff6555dff00+79264]
	(No symbol) [0x0x7ff655399c8c]
	(No symbol) [0x0x7ff655386f6b]
	(No symbol) [0x0x7ff655385e2e]
	(No symbol) [0x0x7ff6553aac44]
	(No symbol) [0x0x7ff6553a9f85]
	(No symbol) [0x0x7ff655440ee7]
	(No symbol) [0x0x7ff655418743]
	(No symbol) [0x0x7ff6553e14c1]
	(No symbol) [0x0x7ff6553e2253]
	GetHandleVerifier [0x0x7ff6558aa2dd+3004797]
	GetHandleVerifier [0x0x7ff6558a472d+2981325]
	GetHandleVerifier [0x0x7ff6558c3380+3107360]
	GetHandleVerifier [0x0x7ff6555faa2e+188622]
	GetHandleVerifier [0x0x7ff6556022bf+219487]
	GetHandleVerifier [0x0x7ff6555e8df4+115860]
	GetHandleVerifier [0x0x7ff6555e8fa9+116297]
	GetHandleVerifier [0x0x7ff6555cf558+11256]
	BaseThreadInitThunk [0x0x7ff80e937374+20]
	RtlUserThreadStart [0x0x7ff81081cc91+33]
 (scraper_thread.py:317)
2025-06-16 14:41:45,806 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 0 benzersiz kullanıcı bulundu (scraper_thread.py:313)
2025-06-16 14:41:47,463 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 14:41:47,464 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 14:41:47,525 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:186)
2025-06-16 14:41:47,528 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 14:41:48,949 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:313)
2025-06-16 14:41:55,670 [INFO] main - 🏁 scraper tamamlandı (main.py:124)
2025-06-16 14:41:57,686 [INFO] main - 🔄 Kullanıcı bulunamadı, scraper tekrar başlatılıyor (main.py:217)
2025-06-16 14:41:59,092 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:41:59,092 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:41:59,108 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:41:59,123 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:41:59,123 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:42:11,139 [INFO] __main__ - 📨 Komut: start (ID: 367) (automation_worker.py:214)
2025-06-16 14:42:11,139 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:42:12,030 [INFO] __main__ - 📨 Komut: start (ID: 367) (automation_worker.py:214)
2025-06-16 14:42:12,030 [INFO] __main__ - ⚠️ Otomasyon zaten çalışıyor (automation_worker.py:119)
2025-06-16 14:42:14,311 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:42:14,311 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 14:42:14,311 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 14:42:14,311 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 14:42:14,311 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 14:42:14,311 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 14:42:14,311 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:269)
2025-06-16 14:42:14,311 [INFO] main - ✅ Scraper thread başlatıldı (main.py:282)
2025-06-16 14:42:14,311 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 367) (automation_worker.py:150)
2025-06-16 14:42:18,611 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:313)
2025-06-16 14:42:18,612 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:313)
2025-06-16 14:42:25,750 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:313)
2025-06-16 14:42:30,558 [INFO] main - 📊 Thread durumu: scraper - Alive: True (main.py:120)
2025-06-16 14:42:47,757 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 215 (scraper_thread.py:313)
2025-06-16 14:42:56,397 [INFO] scraper_thread - [SCRAPER] __mehmet___7 | 135 (scraper_thread.py:313)
2025-06-16 14:43:00,541 [INFO] scraper_thread - [SCRAPER] runbarunn | 16 (scraper_thread.py:313)
2025-06-16 14:43:16,993 [INFO] scraper_thread - [SCRAPER] ellymsliya | 24 (scraper_thread.py:313)
2025-06-16 14:43:30,130 [INFO] main - 📊 Thread durumu: scraper - Alive: True (main.py:120)
2025-06-16 14:44:33,071 [INFO] scraper_thread - [SCRAPER] Süre doldu (1.0 dk) (scraper_thread.py:313)
2025-06-16 14:44:33,073 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 4 benzersiz kullanıcı bulundu (scraper_thread.py:313)
2025-06-16 14:44:36,655 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:313)
2025-06-16 14:44:41,123 [INFO] main - 🏁 scraper tamamlandı (main.py:124)
2025-06-16 14:44:43,139 [INFO] main - 2️⃣ Status Checker başlatılıyor (1 kullanıcı) (main.py:214)
2025-06-16 14:46:31,623 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:46:31,623 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:46:31,644 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:46:31,665 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:46:31,665 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:46:39,420 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 14:46:39,420 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 14:46:39,430 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:186)
2025-06-16 14:46:39,430 [INFO] main - ✅ Otomasyon durduruldu (main.py:195)
2025-06-16 14:46:39,592 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 14:46:46,123 [INFO] __main__ - ✅ Import'lar başarılı (test_status_simple.py:28)
2025-06-16 14:46:46,123 [INFO] __main__ - ✅ DatabaseManager oluşturuldu (test_status_simple.py:32)
2025-06-16 14:46:46,123 [INFO] __main__ - 🔧 StatusCheckerThread oluşturuluyor... (2 kullanıcı) (test_status_simple.py:40)
2025-06-16 14:46:46,139 [INFO] __main__ - ✅ StatusCheckerThread oluşturuldu (test_status_simple.py:49)
2025-06-16 14:46:46,139 [INFO] __main__ - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (test_status_simple.py:50)
2025-06-16 14:46:46,139 [INFO] __main__ - 🔧 Thread daemon: True (test_status_simple.py:51)
2025-06-16 14:46:46,139 [INFO] __main__ - 🚀 StatusCheckerThread başlatılıyor... (test_status_simple.py:53)
2025-06-16 14:46:46,139 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:82)
2025-06-16 14:46:46,139 [INFO] __main__ - ✅ StatusCheckerThread.start() çağrıldı (test_status_simple.py:57)
2025-06-16 14:46:46,139 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 2 (status_checker.py:82)
2025-06-16 14:46:46,139 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 7296 (status_checker.py:82)
2025-06-16 14:46:46,139 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:82)
2025-06-16 14:46:46,139 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Try bloğuna girdi (status_checker.py:82)
2025-06-16 14:46:46,139 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:82)
2025-06-16 14:46:48,139 [INFO] __main__ - 🔧 Thread alive: True (test_status_simple.py:61)
2025-06-16 14:46:48,139 [INFO] __main__ - ⏳ Thread hala çalışıyor... (1/10) (test_status_simple.py:66)
2025-06-16 14:46:49,155 [INFO] __main__ - ⏳ Thread hala çalışıyor... (2/10) (test_status_simple.py:66)
2025-06-16 14:46:49,342 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:82)
2025-06-16 14:46:49,342 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 2 kullanıcı işlenecek (status_checker.py:82)
2025-06-16 14:46:49,342 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'test_user1'}, {'username': 'test_user2'}] (status_checker.py:82)
2025-06-16 14:46:49,342 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:82)
2025-06-16 14:46:49,342 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:82)
2025-06-16 14:46:49,342 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:82)
2025-06-16 14:46:49,342 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:82)
2025-06-16 14:46:49,342 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:82)
2025-06-16 14:46:49,342 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:82)
2025-06-16 14:46:49,342 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:82)
2025-06-16 14:46:49,342 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:82)
2025-06-16 14:46:50,170 [INFO] __main__ - ⏳ Thread hala çalışıyor... (3/10) (test_status_simple.py:66)
2025-06-16 14:46:51,215 [INFO] __main__ - ⏳ Thread hala çalışıyor... (4/10) (test_status_simple.py:66)
2025-06-16 14:46:51,573 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:82)
2025-06-16 14:46:52,323 [INFO] __main__ - ⏳ Thread hala çalışıyor... (5/10) (test_status_simple.py:66)
2025-06-16 14:46:53,374 [INFO] __main__ - ⏳ Thread hala çalışıyor... (6/10) (test_status_simple.py:66)
2025-06-16 14:46:54,404 [INFO] __main__ - ⏳ Thread hala çalışıyor... (7/10) (test_status_simple.py:66)
2025-06-16 14:46:54,613 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:82)
2025-06-16 14:46:54,614 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:82)
2025-06-16 14:46:54,616 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:82)
2025-06-16 14:46:54,617 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:82)
2025-06-16 14:46:55,407 [INFO] __main__ - ⏳ Thread hala çalışıyor... (8/10) (test_status_simple.py:66)
2025-06-16 14:46:56,429 [INFO] __main__ - ⏳ Thread hala çalışıyor... (9/10) (test_status_simple.py:66)
2025-06-16 14:46:57,482 [INFO] __main__ - ⏳ Thread hala çalışıyor... (10/10) (test_status_simple.py:66)
2025-06-16 14:46:58,552 [INFO] __main__ - ✅ Test tamamlandı (test_status_simple.py:72)
2025-06-16 14:50:29,981 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 14:50:30,045 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:50:30,448 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 14:50:43,510 [INFO] __main__ - ✅ Import'lar başarılı (test_status_simple.py:34)
2025-06-16 14:50:43,511 [INFO] __main__ - ✅ DatabaseManager oluşturuldu (test_status_simple.py:38)
2025-06-16 14:50:43,511 [INFO] __main__ - 🔧 StatusCheckerThread oluşturuluyor... (3 kullanıcı) (test_status_simple.py:47)
2025-06-16 14:50:43,512 [INFO] __main__ - 📋 Test edilecek kullanıcılar: (test_status_simple.py:48)
2025-06-16 14:50:43,512 [INFO] __main__ -   1. tarotunsesinden (test_status_simple.py:50)
2025-06-16 14:50:43,512 [INFO] __main__ -   2. theangarabebesi (test_status_simple.py:50)
2025-06-16 14:50:43,512 [INFO] __main__ -   3. runbarunn (test_status_simple.py:50)
2025-06-16 14:50:43,513 [INFO] __main__ - ✅ StatusCheckerThread oluşturuldu (test_status_simple.py:59)
2025-06-16 14:50:43,513 [INFO] __main__ - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (test_status_simple.py:60)
2025-06-16 14:50:43,513 [INFO] __main__ - 🔧 Thread daemon: True (test_status_simple.py:61)
2025-06-16 14:50:43,513 [INFO] __main__ - 🚀 StatusCheckerThread başlatılıyor... (test_status_simple.py:63)
2025-06-16 14:50:43,514 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:82)
2025-06-16 14:50:43,514 [INFO] __main__ - ✅ StatusCheckerThread.start() çağrıldı (test_status_simple.py:67)
2025-06-16 14:50:43,514 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 3 (status_checker.py:82)
2025-06-16 14:50:43,515 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 10820 (status_checker.py:82)
2025-06-16 14:50:43,515 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:82)
2025-06-16 14:50:43,515 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Try bloğuna girdi (status_checker.py:82)
2025-06-16 14:50:43,515 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:82)
2025-06-16 14:50:45,517 [INFO] __main__ - 🔧 Thread alive: True (test_status_simple.py:71)
2025-06-16 14:50:45,517 [INFO] __main__ - ⏳ Status Checker'ın çalışmasını izliyoruz (5 dakika)... (test_status_simple.py:74)
2025-06-16 14:50:45,518 [INFO] __main__ - ⏳ Thread çalışıyor... (0 dakika 0 saniye) (test_status_simple.py:78)
2025-06-16 14:50:46,768 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:82)
2025-06-16 14:50:46,768 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 3 kullanıcı işlenecek (status_checker.py:82)
2025-06-16 14:50:46,768 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'tarotunsesinden'}, {'username': 'theangarabebesi'}, {'username': 'runbarunn'}] (status_checker.py:82)
2025-06-16 14:50:46,769 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:82)
2025-06-16 14:50:46,769 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:82)
2025-06-16 14:50:46,769 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:82)
2025-06-16 14:50:46,769 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:82)
2025-06-16 14:50:46,770 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:82)
2025-06-16 14:50:46,770 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:82)
2025-06-16 14:50:46,770 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:82)
2025-06-16 14:50:46,771 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:82)
2025-06-16 14:50:52,083 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:82)
2025-06-16 14:50:55,256 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:82)
2025-06-16 14:50:55,257 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:82)
2025-06-16 14:50:55,257 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:82)
2025-06-16 14:50:55,259 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:82)
2025-06-16 14:51:05,264 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:82)
2025-06-16 14:51:05,300 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:82)
2025-06-16 14:51:05,745 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:82)
2025-06-16 14:51:05,766 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:82)
2025-06-16 14:51:05,766 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:82)
2025-06-16 14:51:05,767 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:82)
2025-06-16 14:51:09,111 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:82)
2025-06-16 14:51:09,112 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 3 kullanıcı). (status_checker.py:82)
2025-06-16 14:51:14,821 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:82)
2025-06-16 14:51:17,793 [INFO] __main__ - ⏳ Thread çalışıyor... (0 dakika 30 saniye) (test_status_simple.py:78)
2025-06-16 14:51:21,771 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 3 kullanıcı textarea'ya yazıldı. (status_checker.py:82)
2025-06-16 14:51:25,500 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:82)
2025-06-16 14:51:39,944 [WARNING] StatusChecker - [STATUS_CHECKER] ⚠ Tablo 10 sn'de yüklenmedi, parse edilemedi. (status_checker.py:84)
2025-06-16 14:51:49,328 [INFO] __main__ - ⏳ Thread çalışıyor... (1 dakika 0 saniye) (test_status_simple.py:78)
2025-06-16 14:51:50,517 [WARNING] StatusChecker - [STATUS_CHECKER] ⚠ Geri butonu bulunamadı. (status_checker.py:84)
2025-06-16 14:51:55,857 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:82)
2025-06-16 14:51:56,041 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:82)
2025-06-16 14:51:56,042 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:82)
2025-06-16 14:51:56,627 [INFO] __main__ - 🏁 Thread tamamlandı (1 dakika 7 saniyede) (test_status_simple.py:81)
2025-06-16 14:51:56,628 [INFO] __main__ - ✅ Test tamamlandı (test_status_simple.py:88)
2025-06-16 14:52:59,462 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:52:59,462 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:52:59,479 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:52:59,495 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:52:59,496 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:53:13,614 [INFO] __main__ - 📨 Komut: start (ID: 368) (automation_worker.py:214)
2025-06-16 14:53:13,615 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:53:16,791 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:53:16,792 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 14:53:16,793 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 14:53:16,796 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 14:53:16,797 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 14:53:16,799 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 14:53:16,799 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:280)
2025-06-16 14:53:16,801 [INFO] main - ✅ Scraper thread başlatıldı (main.py:293)
2025-06-16 14:53:16,805 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 368) (automation_worker.py:150)
2025-06-16 14:53:24,994 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:313)
2025-06-16 14:53:24,997 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:313)
2025-06-16 14:53:27,572 [ERROR] scraper_thread - [SCRAPER] Scraper döngüsünde hata: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: Unable to receive message from renderer
  (Session info: chrome=137.0.7151.68)
Stacktrace:
	GetHandleVerifier [0x0x7ff6555dfea5+79173]
	GetHandleVerifier [0x0x7ff6555dff00+79264]
	(No symbol) [0x0x7ff655399e5a]
	(No symbol) [0x0x7ff6553872dc]
	(No symbol) [0x0x7ff655386fca]
	(No symbol) [0x0x7ff655384b9f]
	(No symbol) [0x0x7ff6553855ff]
	(No symbol) [0x0x7ff6553942ae]
	(No symbol) [0x0x7ff6553aa671]
	(No symbol) [0x0x7ff6553b17ba]
	(No symbol) [0x0x7ff655385d9d]
	(No symbol) [0x0x7ff6553a9e61]
	(No symbol) [0x0x7ff655441384]
	(No symbol) [0x0x7ff655418743]
	(No symbol) [0x0x7ff6553e14c1]
	(No symbol) [0x0x7ff6553e2253]
	GetHandleVerifier [0x0x7ff6558aa2dd+3004797]
	GetHandleVerifier [0x0x7ff6558a472d+2981325]
	GetHandleVerifier [0x0x7ff6558c3380+3107360]
	GetHandleVerifier [0x0x7ff6555faa2e+188622]
	GetHandleVerifier [0x0x7ff6556022bf+219487]
	GetHandleVerifier [0x0x7ff6555e8df4+115860]
	GetHandleVerifier [0x0x7ff6555e8fa9+116297]
	GetHandleVerifier [0x0x7ff6555cf558+11256]
	BaseThreadInitThunk [0x0x7ff80e937374+20]
	RtlUserThreadStart [0x0x7ff81081cc91+33]
 (scraper_thread.py:317)
2025-06-16 14:53:27,580 [WARNING] scraper_thread - [SCRAPER] Chrome session kaybedildi, yeniden başlatma deneniyor... (scraper_thread.py:315)
2025-06-16 14:53:37,499 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:313)
2025-06-16 14:53:37,500 [INFO] scraper_thread - [SCRAPER] Chrome yeniden başlatıldı (scraper_thread.py:313)
2025-06-16 14:53:44,234 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 0 benzersiz kullanıcı bulundu (scraper_thread.py:313)
2025-06-16 14:53:47,284 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:313)
2025-06-16 14:53:52,201 [INFO] main - 🏁 scraper tamamlandı (main.py:124)
2025-06-16 14:53:54,228 [INFO] main - 🔍 Son 1 dakikada toplam 0 kullanıcı: (main.py:219)
2025-06-16 14:53:54,229 [INFO] main - 🔄 'Bekleniyor' statüsünde kullanıcı bulunamadı, scraper tekrar başlatılıyor (main.py:228)
2025-06-16 14:54:30,143 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 14:54:30,144 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 14:54:30,144 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:186)
2025-06-16 14:54:30,144 [INFO] main - ✅ Otomasyon durduruldu (main.py:195)
2025-06-16 14:54:30,237 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 14:54:37,190 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:54:37,197 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:54:37,208 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:54:37,224 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:54:37,226 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:54:47,296 [INFO] __main__ - 📨 Komut: start (ID: 369) (automation_worker.py:214)
2025-06-16 14:54:47,296 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:54:50,483 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:54:50,483 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 14:54:50,483 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 14:54:50,483 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 14:54:50,483 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 14:54:50,483 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 14:54:50,483 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:280)
2025-06-16 14:54:50,483 [INFO] main - ✅ Scraper thread başlatıldı (main.py:293)
2025-06-16 14:54:50,483 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 369) (automation_worker.py:150)
2025-06-16 14:54:55,162 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:313)
2025-06-16 14:54:55,163 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:313)
2025-06-16 14:55:00,592 [INFO] main - 📊 Thread durumu: scraper - Alive: True (main.py:120)
2025-06-16 14:55:03,061 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:313)
2025-06-16 14:55:07,528 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 220 (scraper_thread.py:313)
2025-06-16 14:55:15,715 [INFO] scraper_thread - [SCRAPER] serayemlek | 14 (scraper_thread.py:313)
2025-06-16 14:55:22,283 [INFO] scraper_thread - [SCRAPER] groweilebusra | 190 (scraper_thread.py:313)
2025-06-16 14:55:27,123 [INFO] scraper_thread - [SCRAPER] yasha__mlbb | 9 (scraper_thread.py:313)
2025-06-16 14:55:30,966 [INFO] main - 📊 Thread durumu: scraper - Alive: True (main.py:120)
2025-06-16 14:55:31,557 [INFO] scraper_thread - [SCRAPER] __mehmet___7 | 157 (scraper_thread.py:313)
2025-06-16 14:55:35,884 [INFO] scraper_thread - [SCRAPER] aylara473 | 3 (scraper_thread.py:313)
2025-06-16 14:55:40,537 [INFO] scraper_thread - [SCRAPER] keniverse0 | 7 (scraper_thread.py:313)
2025-06-16 14:55:45,278 [INFO] scraper_thread - [SCRAPER] sekofitness | 283 (scraper_thread.py:313)
2025-06-16 14:55:50,070 [INFO] scraper_thread - [SCRAPER] tek13basina07ordu34 | 15 (scraper_thread.py:313)
2025-06-16 14:55:54,544 [INFO] scraper_thread - [SCRAPER] b12sizbalik | 29 (scraper_thread.py:313)
2025-06-16 14:55:59,018 [INFO] scraper_thread - [SCRAPER] rnsmert0 | 11 (scraper_thread.py:313)
2025-06-16 14:56:03,230 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 11 benzersiz kullanıcı bulundu (scraper_thread.py:313)
2025-06-16 14:56:06,015 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:313)
2025-06-16 14:56:11,296 [INFO] main - 🏁 scraper tamamlandı (main.py:124)
2025-06-16 14:56:13,371 [INFO] main - 🔍 Son 1 dakikada toplam 10 kullanıcı: (main.py:219)
2025-06-16 14:56:13,382 [INFO] main -   - rnsmert0: Bekleniyor (2025-06-16 14:55:59) (main.py:221)
2025-06-16 14:56:13,385 [INFO] main -   - b12sizbalik: Bekleniyor (2025-06-16 14:55:54) (main.py:221)
2025-06-16 14:56:13,386 [INFO] main -   - tek13basina07ordu34: Bekleniyor (2025-06-16 14:55:50) (main.py:221)
2025-06-16 14:56:13,386 [INFO] main -   - sekofitness: Bekleniyor (2025-06-16 14:55:45) (main.py:221)
2025-06-16 14:56:13,388 [INFO] main -   - keniverse0: Bekleniyor (2025-06-16 14:55:40) (main.py:221)
2025-06-16 14:56:13,388 [INFO] main -   - aylara473: Bekleniyor (2025-06-16 14:55:35) (main.py:221)
2025-06-16 14:56:13,389 [INFO] main -   - __mehmet___7: Bekleniyor (2025-06-16 14:55:31) (main.py:221)
2025-06-16 14:56:13,389 [INFO] main -   - yasha__mlbb: Bekleniyor (2025-06-16 14:55:27) (main.py:221)
2025-06-16 14:56:13,390 [INFO] main -   - groweilebusra: Bekleniyor (2025-06-16 14:55:22) (main.py:221)
2025-06-16 14:56:13,390 [INFO] main -   - serayemlek: Bekleniyor (2025-06-16 14:55:15) (main.py:221)
2025-06-16 14:56:13,391 [INFO] main - 2️⃣ Status Checker başlatılıyor (10 kullanıcı) (main.py:225)
2025-06-16 14:56:35,701 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 14:56:35,702 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 14:56:35,702 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:186)
2025-06-16 14:56:35,702 [INFO] main - ✅ Otomasyon durduruldu (main.py:195)
2025-06-16 14:56:37,709 [INFO] __main__ - ✅ Otomasyon temizlendi (automation_worker.py:97)
2025-06-16 14:56:37,709 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:56:41,062 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:56:41,062 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 14:58:20,165 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 14:58:20,165 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 14:58:20,175 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 14:58:20,190 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 14:58:20,191 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 14:58:44,634 [INFO] __main__ - 📨 Komut: start (ID: 370) (automation_worker.py:214)
2025-06-16 14:58:44,683 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 14:58:48,312 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 14:58:48,312 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 14:58:48,312 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 14:58:48,312 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 14:58:48,312 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 14:58:48,312 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 14:58:48,312 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:280)
2025-06-16 14:58:48,312 [INFO] main - ✅ Scraper thread başlatıldı (main.py:293)
2025-06-16 14:58:48,312 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 370) (automation_worker.py:150)
2025-06-16 14:58:55,128 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:313)
2025-06-16 14:58:55,129 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:313)
2025-06-16 14:59:00,765 [INFO] main - 📊 Thread durumu: scraper - Alive: True (main.py:120)
2025-06-16 14:59:03,093 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:313)
2025-06-16 14:59:15,857 [INFO] scraper_thread - [SCRAPER] sinem6534 | 403 (scraper_thread.py:313)
2025-06-16 14:59:28,648 [INFO] scraper_thread - [SCRAPER] necatitekin756 | 7 (scraper_thread.py:313)
2025-06-16 14:59:33,488 [INFO] scraper_thread - [SCRAPER] dilaysh.n | 8 (scraper_thread.py:313)
2025-06-16 14:59:38,365 [INFO] scraper_thread - [SCRAPER] mat_andron | 10 (scraper_thread.py:313)
2025-06-16 14:59:44,150 [INFO] scraper_thread - [SCRAPER] suulltaaannnn | 2 (scraper_thread.py:313)
2025-06-16 14:59:49,225 [INFO] scraper_thread - [SCRAPER] antepli.63 | 50 (scraper_thread.py:313)
2025-06-16 14:59:53,636 [INFO] scraper_thread - [SCRAPER] tt.bal.pubg | 84 (scraper_thread.py:313)
2025-06-16 14:59:58,342 [INFO] scraper_thread - [SCRAPER] eliffffff7 | 37 (scraper_thread.py:313)
2025-06-16 15:00:03,277 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 8 benzersiz kullanıcı bulundu (scraper_thread.py:313)
2025-06-16 15:00:07,234 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:313)
2025-06-16 15:00:11,984 [INFO] main - 🏁 scraper tamamlandı (main.py:124)
2025-06-16 15:00:13,999 [INFO] main - 🔍 Son 1 dakikada toplam 8 kullanıcı: (main.py:219)
2025-06-16 15:00:13,999 [INFO] main -   - eliffffff7: Bekleniyor (2025-06-16 14:59:58) (main.py:221)
2025-06-16 15:00:13,999 [INFO] main -   - tt.bal.pubg: Bekleniyor (2025-06-16 14:59:53) (main.py:221)
2025-06-16 15:00:13,999 [INFO] main -   - antepli.63: Bekleniyor (2025-06-16 14:59:49) (main.py:221)
2025-06-16 15:00:13,999 [INFO] main -   - suulltaaannnn: Bekleniyor (2025-06-16 14:59:44) (main.py:221)
2025-06-16 15:00:13,999 [INFO] main -   - mat_andron: Bekleniyor (2025-06-16 14:59:38) (main.py:221)
2025-06-16 15:00:13,999 [INFO] main -   - dilaysh.n: Bekleniyor (2025-06-16 14:59:33) (main.py:221)
2025-06-16 15:00:13,999 [INFO] main -   - necatitekin756: Bekleniyor (2025-06-16 14:59:28) (main.py:221)
2025-06-16 15:00:13,999 [INFO] main -   - sinem6534: Bekleniyor (2025-06-16 14:59:15) (main.py:221)
2025-06-16 15:00:13,999 [INFO] main - 2️⃣ Status Checker başlatılıyor (8 kullanıcı) (main.py:225)
2025-06-16 15:02:21,960 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 15:02:21,960 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 15:02:21,974 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 15:02:21,993 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 15:02:21,993 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 15:02:34,290 [INFO] __main__ - 📨 Komut: start (ID: 371) (automation_worker.py:214)
2025-06-16 15:02:34,321 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 15:02:37,564 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 15:02:37,564 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 15:02:37,565 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 15:02:37,567 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 15:02:37,568 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 15:02:37,568 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 15:02:37,568 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:280)
2025-06-16 15:02:37,569 [INFO] main - ✅ Scraper thread başlatıldı (main.py:293)
2025-06-16 15:02:37,573 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 371) (automation_worker.py:150)
2025-06-16 15:02:46,770 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:313)
2025-06-16 15:02:46,770 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:313)
2025-06-16 15:03:00,337 [INFO] main - 📊 Thread durumu: scraper - Alive: True (main.py:120)
2025-06-16 15:03:03,483 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:313)
2025-06-16 15:03:11,135 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 249 (scraper_thread.py:313)
2025-06-16 15:03:20,837 [INFO] scraper_thread - [SCRAPER] nare_pubg2 | 59 (scraper_thread.py:313)
2025-06-16 15:03:29,581 [INFO] scraper_thread - [SCRAPER] sekofitness | 257 (scraper_thread.py:313)
2025-06-16 15:03:35,174 [INFO] scraper_thread - [SCRAPER] rnsmert0 | 110 (scraper_thread.py:313)
2025-06-16 15:03:40,705 [INFO] scraper_thread - [SCRAPER] runbarunn | 15 (scraper_thread.py:313)
2025-06-16 15:03:49,709 [INFO] scraper_thread - [SCRAPER] yasha__mlbb | 6 (scraper_thread.py:313)
2025-06-16 15:03:56,054 [INFO] scraper_thread - [SCRAPER] zeymip1 | 7 (scraper_thread.py:313)
2025-06-16 15:04:00,818 [INFO] scraper_thread - [SCRAPER] zeynommx21 | 14 (scraper_thread.py:313)
2025-06-16 15:04:05,253 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 8 benzersiz kullanıcı bulundu (scraper_thread.py:313)
2025-06-16 15:04:08,033 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:313)
2025-06-16 15:04:12,171 [INFO] main - 🏁 scraper tamamlandı (main.py:124)
2025-06-16 15:04:14,384 [INFO] main - 🔍 Son 1 dakikada toplam 7 kullanıcı: (main.py:219)
2025-06-16 15:04:14,385 [INFO] main -   - zeynommx21: Bekleniyor (2025-06-16 15:04:00) (main.py:221)
2025-06-16 15:04:14,385 [INFO] main -   - zeymip1: Bekleniyor (2025-06-16 15:03:56) (main.py:221)
2025-06-16 15:04:14,385 [INFO] main -   - yasha__mlbb: Bekleniyor (2025-06-16 15:03:49) (main.py:221)
2025-06-16 15:04:14,386 [INFO] main -   - runbarunn: Bekleniyor (2025-06-16 15:03:40) (main.py:221)
2025-06-16 15:04:14,386 [INFO] main -   - rnsmert0: Bekleniyor (2025-06-16 15:03:35) (main.py:221)
2025-06-16 15:04:14,386 [INFO] main -   - sekofitness: Bekleniyor (2025-06-16 15:03:29) (main.py:221)
2025-06-16 15:04:14,386 [INFO] main -   - nare_pubg2: Bekleniyor (2025-06-16 15:03:20) (main.py:221)
2025-06-16 15:04:14,386 [INFO] main - 2️⃣ Status Checker başlatılıyor (7 kullanıcı) (main.py:225)
2025-06-16 15:04:14,387 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:302)
2025-06-16 15:04:14,387 [INFO] main - 🔧 Phase lock alınıyor... (main.py:308)
2025-06-16 15:05:14,793 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 15:05:14,794 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 15:05:14,805 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 15:05:14,825 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 15:05:14,831 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 15:05:31,133 [INFO] __main__ - 📨 Komut: start (ID: 372) (automation_worker.py:214)
2025-06-16 15:05:31,133 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 15:05:34,333 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 15:05:34,333 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 15:05:34,333 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 15:05:34,336 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 15:05:34,336 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 15:05:34,337 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 15:05:34,337 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:283)
2025-06-16 15:05:34,338 [INFO] main - ✅ Scraper thread başlatıldı (main.py:296)
2025-06-16 15:05:34,342 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 372) (automation_worker.py:150)
2025-06-16 15:05:43,388 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:313)
2025-06-16 15:05:43,389 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:313)
2025-06-16 15:05:49,827 [ERROR] scraper_thread - [SCRAPER] Scraper döngüsünde hata: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: Unable to receive message from renderer
  (Session info: chrome=137.0.7151.68)
Stacktrace:
	GetHandleVerifier [0x0x7ff6555dfea5+79173]
	GetHandleVerifier [0x0x7ff6555dff00+79264]
	(No symbol) [0x0x7ff655399e5a]
	(No symbol) [0x0x7ff6553872dc]
	(No symbol) [0x0x7ff655386fca]
	(No symbol) [0x0x7ff655384b9f]
	(No symbol) [0x0x7ff6553855ff]
	(No symbol) [0x0x7ff6553942ae]
	(No symbol) [0x0x7ff6553aa671]
	(No symbol) [0x0x7ff6553b17ba]
	(No symbol) [0x0x7ff655385d9d]
	(No symbol) [0x0x7ff6553a9e61]
	(No symbol) [0x0x7ff655441384]
	(No symbol) [0x0x7ff655418743]
	(No symbol) [0x0x7ff6553e14c1]
	(No symbol) [0x0x7ff6553e2253]
	GetHandleVerifier [0x0x7ff6558aa2dd+3004797]
	GetHandleVerifier [0x0x7ff6558a472d+2981325]
	GetHandleVerifier [0x0x7ff6558c3380+3107360]
	GetHandleVerifier [0x0x7ff6555faa2e+188622]
	GetHandleVerifier [0x0x7ff6556022bf+219487]
	GetHandleVerifier [0x0x7ff6555e8df4+115860]
	GetHandleVerifier [0x0x7ff6555e8fa9+116297]
	GetHandleVerifier [0x0x7ff6555cf558+11256]
	BaseThreadInitThunk [0x0x7ff80e937374+20]
	RtlUserThreadStart [0x0x7ff81081cc91+33]
 (scraper_thread.py:317)
2025-06-16 15:05:49,829 [WARNING] scraper_thread - [SCRAPER] Chrome session kaybedildi, yeniden başlatma deneniyor... (scraper_thread.py:315)
2025-06-16 15:05:55,891 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:313)
2025-06-16 15:05:55,893 [INFO] scraper_thread - [SCRAPER] Chrome yeniden başlatıldı (scraper_thread.py:313)
2025-06-16 15:06:00,165 [INFO] main - 📊 Thread durumu: scraper - Alive: True (main.py:120)
2025-06-16 15:06:17,934 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 0 benzersiz kullanıcı bulundu (scraper_thread.py:313)
2025-06-16 15:06:21,802 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:313)
2025-06-16 15:06:26,862 [INFO] main - 🏁 scraper tamamlandı (main.py:124)
2025-06-16 15:09:16,259 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 15:09:16,259 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 15:09:16,259 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:189)
2025-06-16 15:09:16,259 [INFO] main - ✅ Otomasyon durduruldu (main.py:198)
2025-06-16 15:09:16,675 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 15:09:21,688 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 15:09:21,690 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 15:09:21,701 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 15:09:21,712 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 15:09:21,712 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 15:09:25,735 [INFO] __main__ - 📨 Komut: start (ID: 373) (automation_worker.py:214)
2025-06-16 15:09:25,735 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 15:09:28,936 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 15:09:28,936 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 15:09:28,936 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 15:09:28,936 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 15:09:28,936 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 15:09:28,936 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 15:09:28,936 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:283)
2025-06-16 15:09:28,936 [INFO] main - ✅ Scraper thread başlatıldı (main.py:296)
2025-06-16 15:09:28,936 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 373) (automation_worker.py:150)
2025-06-16 15:09:30,952 [INFO] main - 📊 Thread durumu: scraper - Alive: True (main.py:120)
2025-06-16 15:09:35,046 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:313)
2025-06-16 15:09:35,046 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:313)
2025-06-16 15:09:42,800 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:313)
2025-06-16 15:09:50,247 [INFO] scraper_thread - [SCRAPER] tarotunsesinden | 200 (scraper_thread.py:313)
2025-06-16 15:09:55,639 [INFO] scraper_thread - [SCRAPER] eliffffff7 | 30 (scraper_thread.py:313)
2025-06-16 15:10:00,692 [INFO] scraper_thread - [SCRAPER] tolgaayedeek_ | 4 (scraper_thread.py:313)
2025-06-16 15:10:05,004 [INFO] scraper_thread - [SCRAPER] lxl_jiyan_lxl | 7 (scraper_thread.py:313)
2025-06-16 15:10:10,081 [INFO] scraper_thread - [SCRAPER] keniverse0 | 10 (scraper_thread.py:313)
2025-06-16 15:10:15,030 [INFO] scraper_thread - [SCRAPER] hasan..yildirim | 2 (scraper_thread.py:313)
2025-06-16 15:10:19,436 [INFO] scraper_thread - [SCRAPER] ibrahim1.234 | 8 (scraper_thread.py:313)
2025-06-16 15:10:24,717 [INFO] scraper_thread - [SCRAPER] hakanaydoan6 | 1 (scraper_thread.py:313)
2025-06-16 15:10:30,186 [INFO] scraper_thread - [SCRAPER] milenaaaj1 | 65 (scraper_thread.py:313)
2025-06-16 15:10:39,342 [INFO] scraper_thread - [SCRAPER] tuanabeen41 | 0 (scraper_thread.py:313)
2025-06-16 15:10:43,846 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 10 benzersiz kullanıcı bulundu (scraper_thread.py:313)
2025-06-16 15:10:47,672 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:313)
2025-06-16 15:10:53,702 [INFO] main - 🏁 scraper tamamlandı (main.py:124)
2025-06-16 15:12:43,767 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 15:12:43,767 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 15:12:43,894 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:189)
2025-06-16 15:12:44,038 [INFO] main - ✅ Otomasyon durduruldu (main.py:198)
2025-06-16 15:12:46,344 [INFO] __main__ - ✅ Otomasyon temizlendi (automation_worker.py:97)
2025-06-16 15:12:46,362 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 15:12:50,247 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 15:12:50,962 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 15:17:33,238 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 15:17:33,239 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 15:17:33,249 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 15:17:33,269 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 15:17:33,272 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 15:18:47,331 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 15:18:47,331 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 15:18:47,341 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 15:18:47,355 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 15:18:47,356 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 15:18:57,655 [INFO] __main__ - 📨 Komut: start (ID: 374) (automation_worker.py:214)
2025-06-16 15:18:57,655 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 15:19:00,842 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 15:19:00,842 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 15:19:00,842 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 15:19:00,842 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 15:19:00,842 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 15:19:00,842 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 15:19:00,842 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:283)
2025-06-16 15:19:00,842 [INFO] main - ✅ Scraper thread başlatıldı (main.py:296)
2025-06-16 15:19:00,842 [INFO] main - 📊 Thread durumu: scraper - Alive: True (main.py:120)
2025-06-16 15:19:00,842 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 374) (automation_worker.py:150)
2025-06-16 15:19:07,456 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:313)
2025-06-16 15:19:07,458 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:313)
2025-06-16 15:19:10,952 [ERROR] scraper_thread - [SCRAPER] Scraper döngüsünde hata: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: Unable to receive message from renderer
  (Session info: chrome=137.0.7151.68)
Stacktrace:
	GetHandleVerifier [0x0x7ff6555dfea5+79173]
	GetHandleVerifier [0x0x7ff6555dff00+79264]
	(No symbol) [0x0x7ff655399e5a]
	(No symbol) [0x0x7ff6553872dc]
	(No symbol) [0x0x7ff655386fca]
	(No symbol) [0x0x7ff655384b9f]
	(No symbol) [0x0x7ff6553855ff]
	(No symbol) [0x0x7ff6553942ae]
	(No symbol) [0x0x7ff6553aa671]
	(No symbol) [0x0x7ff6553b17ba]
	(No symbol) [0x0x7ff655385d9d]
	(No symbol) [0x0x7ff6553a9e61]
	(No symbol) [0x0x7ff655441384]
	(No symbol) [0x0x7ff655418743]
	(No symbol) [0x0x7ff6553e14c1]
	(No symbol) [0x0x7ff6553e2253]
	GetHandleVerifier [0x0x7ff6558aa2dd+3004797]
	GetHandleVerifier [0x0x7ff6558a472d+2981325]
	GetHandleVerifier [0x0x7ff6558c3380+3107360]
	GetHandleVerifier [0x0x7ff6555faa2e+188622]
	GetHandleVerifier [0x0x7ff6556022bf+219487]
	GetHandleVerifier [0x0x7ff6555e8df4+115860]
	GetHandleVerifier [0x0x7ff6555e8fa9+116297]
	GetHandleVerifier [0x0x7ff6555cf558+11256]
	BaseThreadInitThunk [0x0x7ff80e937374+20]
	RtlUserThreadStart [0x0x7ff81081cc91+33]
 (scraper_thread.py:317)
2025-06-16 15:19:10,952 [WARNING] scraper_thread - [SCRAPER] Chrome session kaybedildi, yeniden başlatma deneniyor... (scraper_thread.py:315)
2025-06-16 15:19:15,568 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:313)
2025-06-16 15:19:15,569 [INFO] scraper_thread - [SCRAPER] Chrome yeniden başlatıldı (scraper_thread.py:313)
2025-06-16 15:19:28,642 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 0 benzersiz kullanıcı bulundu (scraper_thread.py:313)
2025-06-16 15:19:31,546 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:313)
2025-06-16 15:19:37,283 [INFO] main - 🏁 scraper tamamlandı (main.py:124)
2025-06-16 15:20:19,284 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 15:20:19,284 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 15:20:19,284 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:189)
2025-06-16 15:20:19,284 [INFO] main - ✅ Otomasyon durduruldu (main.py:198)
2025-06-16 15:20:19,475 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 15:20:25,170 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 15:20:25,170 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 15:20:25,170 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 15:20:25,196 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 15:20:25,196 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 15:20:31,233 [INFO] __main__ - 📨 Komut: start (ID: 375) (automation_worker.py:214)
2025-06-16 15:20:31,233 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 15:20:34,452 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 15:20:34,452 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 15:20:34,452 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 15:20:34,452 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 15:20:34,452 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 15:20:34,452 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 15:20:34,452 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:283)
2025-06-16 15:20:34,452 [INFO] main - ✅ Scraper thread başlatıldı (main.py:296)
2025-06-16 15:20:34,452 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 375) (automation_worker.py:150)
2025-06-16 15:20:39,265 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:313)
2025-06-16 15:20:39,266 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:313)
2025-06-16 15:20:47,705 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:313)
2025-06-16 15:20:53,108 [INFO] scraper_thread - [SCRAPER] theege61 | 170 (scraper_thread.py:313)
2025-06-16 15:20:59,391 [INFO] scraper_thread - [SCRAPER] yedekhesap132 | 38 (scraper_thread.py:313)
2025-06-16 15:21:00,758 [INFO] main - 📊 Thread durumu: scraper - Alive: True (main.py:120)
2025-06-16 15:21:04,690 [INFO] scraper_thread - [SCRAPER] keniverse0 | 9 (scraper_thread.py:313)
2025-06-16 15:21:10,317 [INFO] scraper_thread - [SCRAPER] derinsuxxydkx | 59 (scraper_thread.py:313)
2025-06-16 15:21:14,420 [INFO] scraper_thread - [SCRAPER] necatitekin756 | 9 (scraper_thread.py:313)
2025-06-16 15:21:19,743 [INFO] scraper_thread - [SCRAPER] nisosdiyorlar | 9 (scraper_thread.py:313)
2025-06-16 15:21:25,242 [INFO] scraper_thread - [SCRAPER] benalafan | 42 (scraper_thread.py:313)
2025-06-16 15:21:30,422 [INFO] scraper_thread - [SCRAPER] rafetgngr0626 | 2 (scraper_thread.py:313)
2025-06-16 15:21:30,945 [INFO] main - 📊 Thread durumu: scraper - Alive: True (main.py:120)
2025-06-16 15:21:35,891 [INFO] scraper_thread - [SCRAPER] tt.bal.pubg | 154 (scraper_thread.py:313)
2025-06-16 15:21:41,076 [INFO] scraper_thread - [SCRAPER] melisamungan47 | 50 (scraper_thread.py:313)
2025-06-16 15:21:45,378 [INFO] scraper_thread - [SCRAPER] eceshaw | 82 (scraper_thread.py:313)
2025-06-16 15:21:50,330 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 11 benzersiz kullanıcı bulundu (scraper_thread.py:313)
2025-06-16 15:21:54,280 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:313)
2025-06-16 15:21:59,077 [INFO] main - 🏁 scraper tamamlandı (main.py:124)
2025-06-16 15:26:01,661 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 15:26:01,662 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 15:26:01,671 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 15:26:01,681 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 15:26:01,682 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 15:30:05,573 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 15:30:05,575 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 15:30:05,575 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:189)
2025-06-16 15:30:05,575 [INFO] main - ✅ Otomasyon durduruldu (main.py:198)
2025-06-16 15:30:06,007 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 15:30:12,982 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 15:30:12,983 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 15:30:12,993 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 15:30:13,006 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 15:30:13,006 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 15:30:19,105 [INFO] __main__ - 📨 Komut: start (ID: 376) (automation_worker.py:214)
2025-06-16 15:30:19,106 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 15:30:22,363 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 15:30:22,363 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 15:30:22,364 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 15:30:22,367 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 15:30:22,367 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 15:30:22,368 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 15:30:22,368 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:300)
2025-06-16 15:30:22,370 [INFO] main - ✅ Scraper thread başlatıldı (main.py:314)
2025-06-16 15:30:22,377 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 376) (automation_worker.py:150)
2025-06-16 15:30:26,926 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-06-16 15:30:26,927 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-06-16 15:30:30,634 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-06-16 15:30:36,736 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-06-16 15:31:00,538 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-06-16 15:31:04,308 [INFO] scraper_thread - [SCRAPER] theege61 | 111 (scraper_thread.py:321)
2025-06-16 15:31:09,800 [INFO] scraper_thread - [SCRAPER] mehmetmusiccc | 6 (scraper_thread.py:321)
2025-06-16 15:31:14,433 [INFO] scraper_thread - [SCRAPER] bekolololo | 59 (scraper_thread.py:321)
2025-06-16 15:31:18,954 [INFO] scraper_thread - [SCRAPER] nisosdiyorlar | 7 (scraper_thread.py:321)
2025-06-16 15:31:24,435 [INFO] scraper_thread - [SCRAPER] keniverse0 | 14 (scraper_thread.py:321)
2025-06-16 15:31:30,076 [INFO] scraper_thread - [SCRAPER] wigeex | 10 (scraper_thread.py:321)
2025-06-16 15:31:30,831 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-06-16 15:31:36,138 [INFO] scraper_thread - [SCRAPER] dilayshi | 93 (scraper_thread.py:321)
2025-06-16 15:31:41,207 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 7 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-06-16 15:31:45,424 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-06-16 15:31:45,426 [INFO] main - 🔍 Son 1 dakikada toplam 7 kullanıcı: (main.py:239)
2025-06-16 15:31:45,427 [INFO] main -   - dilayshi: Bekleniyor (2025-06-16 15:31:36) (main.py:241)
2025-06-16 15:31:45,428 [INFO] main -   - wigeex: Bekleniyor (2025-06-16 15:31:30) (main.py:241)
2025-06-16 15:31:45,428 [INFO] main -   - keniverse0: Bekleniyor (2025-06-16 15:31:24) (main.py:241)
2025-06-16 15:31:45,428 [INFO] main -   - nisosdiyorlar: Bekleniyor (2025-06-16 15:31:18) (main.py:241)
2025-06-16 15:31:45,429 [INFO] main -   - bekolololo: Bekleniyor (2025-06-16 15:31:14) (main.py:241)
2025-06-16 15:31:45,429 [INFO] main -   - mehmetmusiccc: Bekleniyor (2025-06-16 15:31:09) (main.py:241)
2025-06-16 15:31:45,429 [INFO] main -   - theege61: Bekleniyor (2025-06-16 15:31:04) (main.py:241)
2025-06-16 15:31:45,430 [INFO] main - 2️⃣ Status Checker başlatılıyor (7 kullanıcı) (main.py:245)
2025-06-16 15:31:45,430 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:323)
2025-06-16 15:31:45,430 [INFO] main - 🔧 Phase lock alınıyor... (main.py:329)
2025-06-16 15:31:45,431 [INFO] main - 🔧 Phase lock alındı (main.py:331)
2025-06-16 15:31:45,431 [WARNING] main - ⚠️ Başka thread aktif: <ScraperThread(Thread-6, started daemon 8728)>, status checker bekliyor (main.py:334)
2025-06-16 15:31:45,432 [WARNING] main - ⚠️ Thread alive: True (main.py:335)
2025-06-16 15:31:45,432 [WARNING] main - ⚠️ Current phase: scraper (main.py:336)
2025-06-16 15:31:46,939 [INFO] main - 🏁 scraper tamamlandı (finished flag) (main.py:134)
2025-06-16 15:31:46,939 [INFO] main - 🔧 Thread temizleniyor: scraper (main.py:144)
2025-06-16 15:31:46,940 [INFO] main - ✅ Thread temizlendi (main.py:147)
2025-06-16 15:31:48,947 [INFO] main - 🔄 scraper sonrası işlem başlatılıyor... (main.py:156)
2025-06-16 15:31:48,950 [INFO] main - 🔍 Son 1 dakikada toplam 7 kullanıcı: (main.py:239)
2025-06-16 15:31:48,950 [INFO] main -   - dilayshi: Bekleniyor (2025-06-16 15:31:36) (main.py:241)
2025-06-16 15:31:48,951 [INFO] main -   - wigeex: Bekleniyor (2025-06-16 15:31:30) (main.py:241)
2025-06-16 15:31:48,951 [INFO] main -   - keniverse0: Bekleniyor (2025-06-16 15:31:24) (main.py:241)
2025-06-16 15:31:48,951 [INFO] main -   - nisosdiyorlar: Bekleniyor (2025-06-16 15:31:18) (main.py:241)
2025-06-16 15:31:48,951 [INFO] main -   - bekolololo: Bekleniyor (2025-06-16 15:31:14) (main.py:241)
2025-06-16 15:31:48,952 [INFO] main -   - mehmetmusiccc: Bekleniyor (2025-06-16 15:31:09) (main.py:241)
2025-06-16 15:31:48,952 [INFO] main -   - theege61: Bekleniyor (2025-06-16 15:31:04) (main.py:241)
2025-06-16 15:31:48,952 [INFO] main - 2️⃣ Status Checker başlatılıyor (7 kullanıcı) (main.py:245)
2025-06-16 15:31:48,953 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:323)
2025-06-16 15:31:48,953 [INFO] main - 🔧 Phase lock alınıyor... (main.py:329)
2025-06-16 15:35:22,096 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 15:35:22,097 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 15:35:22,097 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:206)
2025-06-16 15:35:22,098 [INFO] main - ✅ Otomasyon durduruldu (main.py:215)
2025-06-16 15:35:24,107 [INFO] __main__ - ✅ Otomasyon temizlendi (automation_worker.py:97)
2025-06-16 15:35:24,107 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 15:35:27,500 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 15:35:27,500 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 15:35:53,858 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 15:35:53,860 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 15:35:53,927 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 15:35:53,999 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 15:35:54,020 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 15:36:04,457 [INFO] __main__ - 📨 Komut: start (ID: 377) (automation_worker.py:214)
2025-06-16 15:36:04,457 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 15:36:07,663 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 15:36:07,667 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 15:36:07,671 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 15:36:07,675 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 15:36:07,675 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 15:36:07,678 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 15:36:07,678 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:324)
2025-06-16 15:36:07,712 [INFO] main - ✅ Scraper thread başlatıldı (main.py:338)
2025-06-16 15:36:07,724 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 377) (automation_worker.py:150)
2025-06-16 15:36:14,429 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-06-16 15:36:14,430 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-06-16 15:36:23,035 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-06-16 15:36:26,468 [INFO] scraper_thread - [SCRAPER] theege61 | 126 (scraper_thread.py:321)
2025-06-16 15:36:30,212 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-06-16 15:36:36,959 [INFO] scraper_thread - [SCRAPER] dilayshi | 114 (scraper_thread.py:321)
2025-06-16 15:36:42,644 [INFO] scraper_thread - [SCRAPER] thejosef6 | 5 (scraper_thread.py:321)
2025-06-16 15:36:46,931 [INFO] scraper_thread - [SCRAPER] medsuuus | 23 (scraper_thread.py:321)
2025-06-16 15:36:50,945 [INFO] scraper_thread - [SCRAPER] bekolololo | 39 (scraper_thread.py:321)
2025-06-16 15:36:54,831 [INFO] scraper_thread - [SCRAPER] nisosdiyorlar | 11 (scraper_thread.py:321)
2025-06-16 15:37:00,110 [INFO] scraper_thread - [SCRAPER] tuncay.ahi65 | 146 (scraper_thread.py:321)
2025-06-16 15:37:00,404 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-06-16 15:37:05,288 [INFO] scraper_thread - [SCRAPER] keniverse0 | 9 (scraper_thread.py:321)
2025-06-16 15:37:10,180 [INFO] scraper_thread - [SCRAPER] runbarunn | 17 (scraper_thread.py:321)
2025-06-16 15:37:14,607 [INFO] scraper_thread - [SCRAPER] cool_suu | 10 (scraper_thread.py:321)
2025-06-16 15:37:20,104 [INFO] scraper_thread - [SCRAPER] bjk.esra1903 | 39 (scraper_thread.py:321)
2025-06-16 15:37:24,766 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 11 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-06-16 15:37:27,452 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-06-16 15:37:27,452 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:224)
2025-06-16 15:37:27,453 [INFO] main - ✅ Scraper thread temizlendi (main.py:230)
2025-06-16 15:37:27,455 [INFO] main - 🔍 Son 1 dakikada toplam 10 kullanıcı: (main.py:247)
2025-06-16 15:37:27,455 [INFO] main -   - bjk.esra1903: Bekleniyor (2025-06-16 15:37:20) (main.py:249)
2025-06-16 15:37:27,455 [INFO] main -   - cool_suu: Bekleniyor (2025-06-16 15:37:14) (main.py:249)
2025-06-16 15:37:27,456 [INFO] main -   - runbarunn: Bekleniyor (2025-06-16 15:37:10) (main.py:249)
2025-06-16 15:37:27,456 [INFO] main -   - keniverse0: Bekleniyor (2025-06-16 15:37:05) (main.py:249)
2025-06-16 15:37:27,456 [INFO] main -   - tuncay.ahi65: Bekleniyor (2025-06-16 15:37:00) (main.py:249)
2025-06-16 15:37:27,457 [INFO] main -   - nisosdiyorlar: Bekleniyor (2025-06-16 15:36:54) (main.py:249)
2025-06-16 15:37:27,457 [INFO] main -   - bekolololo: Bekleniyor (2025-06-16 15:36:50) (main.py:249)
2025-06-16 15:37:27,457 [INFO] main -   - medsuuus: Bekleniyor (2025-06-16 15:36:46) (main.py:249)
2025-06-16 15:37:27,458 [INFO] main -   - thejosef6: Bekleniyor (2025-06-16 15:36:42) (main.py:249)
2025-06-16 15:37:27,458 [INFO] main -   - dilayshi: Bekleniyor (2025-06-16 15:36:36) (main.py:249)
2025-06-16 15:37:27,459 [INFO] main - 2️⃣ Status Checker başlatılıyor (10 kullanıcı) (main.py:253)
2025-06-16 15:37:27,459 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:347)
2025-06-16 15:37:27,460 [INFO] main - 🔧 Phase lock alınıyor... (main.py:353)
2025-06-16 15:37:27,460 [INFO] main - 🔧 Phase lock alındı (main.py:355)
2025-06-16 15:37:27,462 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:363)
2025-06-16 15:37:27,462 [INFO] main - 2️⃣ Status Checker başlatılıyor (10 kullanıcı) (main.py:366)
2025-06-16 15:37:27,463 [INFO] main - 📋 Kullanıcılar: ['dilayshi', 'thejosef6', 'medsuuus', 'bekolololo', 'nisosdiyorlar', 'tuncay.ahi65', 'keniverse0', 'runbarunn', 'cool_suu', 'bjk.esra1903'] (main.py:367)
2025-06-16 15:37:27,463 [INFO] main - 📋 Publishers formatı: [{'username': 'dilayshi'}, {'username': 'thejosef6'}, {'username': 'medsuuus'}, {'username': 'bekolololo'}, {'username': 'nisosdiyorlar'}, {'username': 'tuncay.ahi65'}, {'username': 'keniverse0'}, {'username': 'runbarunn'}, {'username': 'cool_suu'}, {'username': 'bjk.esra1903'}] (main.py:371)
2025-06-16 15:37:27,464 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:373)
2025-06-16 15:37:27,465 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:383)
2025-06-16 15:37:27,465 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:384)
2025-06-16 15:37:27,466 [INFO] main - 🔧 Thread daemon: True (main.py:385)
2025-06-16 15:37:27,466 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:394)
2025-06-16 15:37:27,469 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-06-16 15:37:27,469 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:398)
2025-06-16 15:37:27,470 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 10 (status_checker.py:84)
2025-06-16 15:37:27,470 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:407)
2025-06-16 15:37:27,471 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 3504 (status_checker.py:84)
2025-06-16 15:37:27,473 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-06-16 15:37:27,474 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-06-16 15:37:27,475 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-06-16 15:37:27,476 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-06-16 15:37:30,566 [INFO] main - 📊 Thread durumu: status_checker - Alive: True, Finished: False (main.py:126)
2025-06-16 15:37:30,799 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-06-16 15:37:30,800 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 10 kullanıcı işlenecek (status_checker.py:84)
2025-06-16 15:37:30,800 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'dilayshi'}, {'username': 'thejosef6'}, {'username': 'medsuuus'}] (status_checker.py:84)
2025-06-16 15:37:30,801 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-06-16 15:37:30,802 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-06-16 15:37:30,802 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-06-16 15:37:30,803 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-06-16 15:37:30,804 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-06-16 15:37:30,804 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-06-16 15:37:30,807 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-06-16 15:37:30,808 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-06-16 15:37:33,216 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 15:37:36,240 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-06-16 15:37:36,241 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 15:37:36,241 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-06-16 15:37:36,243 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-06-16 15:37:38,997 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-06-16 15:37:38,997 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-06-16 15:37:39,043 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-06-16 15:37:39,058 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-06-16 15:37:39,059 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-06-16 15:37:39,059 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-06-16 15:37:43,556 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-06-16 15:37:43,557 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 10 kullanıcı). (status_checker.py:84)
2025-06-16 15:37:48,293 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-06-16 15:37:53,310 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 10 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-06-16 15:37:57,314 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-06-16 15:37:58,683 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 10 (status_checker.py:84)
2025-06-16 15:37:58,703 [INFO] StatusChecker - [STATUS_CHECKER] 💾 10 kaydın durumu güncellendi. (status_checker.py:84)
2025-06-16 15:38:00,920 [INFO] main - 📊 Thread durumu: status_checker - Alive: True, Finished: False (main.py:126)
2025-06-16 15:38:02,689 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-06-16 15:38:08,358 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-06-16 15:38:08,550 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-06-16 15:38:08,550 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-06-16 15:38:08,551 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:268)
2025-06-16 15:38:08,551 [INFO] main - ✅ Status Checker thread temizlendi (main.py:274)
2025-06-16 15:38:08,594 [INFO] main - 3️⃣ Message Sender başlatılıyor (2 kullanıcı) (main.py:286)
2025-06-16 15:38:08,595 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:433)
2025-06-16 15:39:18,469 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 15:39:18,470 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 15:39:18,471 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:206)
2025-06-16 15:39:18,471 [INFO] main - ✅ Otomasyon durduruldu (main.py:215)
2025-06-16 15:39:18,668 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 15:39:58,823 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 15:39:58,827 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 15:39:58,837 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 15:39:58,849 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 15:39:58,850 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 15:40:43,151 [INFO] __main__ - 📨 Komut: start (ID: 378) (automation_worker.py:214)
2025-06-16 15:42:55,464 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 15:42:58,655 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 15:43:23,662 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 15:43:23,663 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 15:43:23,668 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 15:43:23,668 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 15:43:23,669 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 15:43:23,669 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:324)
2025-06-16 15:43:23,670 [INFO] main - ✅ Scraper thread başlatıldı (main.py:338)
2025-06-16 15:43:23,676 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 378) (automation_worker.py:150)
2025-06-16 15:43:25,688 [INFO] __main__ - 📨 Komut: start (ID: 379) (automation_worker.py:214)
2025-06-16 15:43:25,689 [INFO] __main__ - ⚠️ Otomasyon zaten çalışıyor (automation_worker.py:119)
2025-06-16 15:43:27,954 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-06-16 15:43:33,018 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-06-16 15:43:39,555 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-06-16 15:43:46,609 [INFO] scraper_thread - [SCRAPER] theege61 | 157 (scraper_thread.py:321)
2025-06-16 15:43:50,646 [INFO] scraper_thread - [SCRAPER] serayemlek | 16 (scraper_thread.py:321)
2025-06-16 15:43:55,384 [INFO] scraper_thread - [SCRAPER] woxbultt | 24 (scraper_thread.py:321)
2025-06-16 15:44:00,545 [INFO] scraper_thread - [SCRAPER] gkhn6012 | 14 (scraper_thread.py:321)
2025-06-16 15:44:09,931 [INFO] scraper_thread - [SCRAPER] seleenekici | 13 (scraper_thread.py:321)
2025-06-16 15:44:30,921 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-06-16 15:44:47,429 [INFO] scraper_thread - [SCRAPER] Süre doldu (1.0 dk) (scraper_thread.py:321)
2025-06-16 15:44:47,430 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 5 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-06-16 15:44:50,001 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-06-16 15:44:50,001 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:224)
2025-06-16 15:44:50,001 [INFO] main - ✅ Scraper thread temizlendi (main.py:230)
2025-06-16 15:44:50,004 [INFO] main - 🔍 Son 1 dakikada toplam 4 kullanıcı: (main.py:247)
2025-06-16 15:44:50,004 [INFO] main -   - seleenekici: Bekleniyor (2025-06-16 15:44:47) (main.py:249)
2025-06-16 15:44:50,004 [INFO] main -   - gkhn6012: Bekleniyor (2025-06-16 15:44:00) (main.py:249)
2025-06-16 15:44:50,005 [INFO] main -   - woxbultt: Bekleniyor (2025-06-16 15:43:55) (main.py:249)
2025-06-16 15:44:50,005 [INFO] main -   - serayemlek: Bekleniyor (2025-06-16 15:43:50) (main.py:249)
2025-06-16 15:44:50,006 [INFO] main - 2️⃣ Status Checker başlatılıyor (4 kullanıcı) (main.py:253)
2025-06-16 15:44:50,006 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:347)
2025-06-16 15:44:50,006 [INFO] main - 🔧 Phase lock alınıyor... (main.py:353)
2025-06-16 15:44:50,007 [INFO] main - 🔧 Phase lock alındı (main.py:355)
2025-06-16 15:44:50,007 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:363)
2025-06-16 15:44:50,008 [INFO] main - 2️⃣ Status Checker başlatılıyor (4 kullanıcı) (main.py:366)
2025-06-16 15:44:50,008 [INFO] main - 📋 Kullanıcılar: ['serayemlek', 'woxbultt', 'gkhn6012', 'seleenekici'] (main.py:367)
2025-06-16 15:44:50,009 [INFO] main - 📋 Publishers formatı: [{'username': 'serayemlek'}, {'username': 'woxbultt'}, {'username': 'gkhn6012'}, {'username': 'seleenekici'}] (main.py:371)
2025-06-16 15:44:50,009 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:373)
2025-06-16 15:44:50,010 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:383)
2025-06-16 15:44:50,010 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:384)
2025-06-16 15:44:50,010 [INFO] main - 🔧 Thread daemon: True (main.py:385)
2025-06-16 15:44:50,011 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:394)
2025-06-16 15:44:50,012 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-06-16 15:44:50,012 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:398)
2025-06-16 15:44:50,012 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 4 (status_checker.py:84)
2025-06-16 15:44:50,013 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 7896 (status_checker.py:84)
2025-06-16 15:44:50,013 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:407)
2025-06-16 15:44:50,013 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-06-16 15:44:50,014 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-06-16 15:44:50,015 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-06-16 15:44:50,015 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-06-16 15:44:53,173 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-06-16 15:44:53,173 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 4 kullanıcı işlenecek (status_checker.py:84)
2025-06-16 15:44:53,175 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'serayemlek'}, {'username': 'woxbultt'}, {'username': 'gkhn6012'}] (status_checker.py:84)
2025-06-16 15:44:53,175 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-06-16 15:44:53,175 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-06-16 15:44:53,176 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-06-16 15:44:53,176 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-06-16 15:44:53,177 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-06-16 15:44:53,177 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-06-16 15:44:53,178 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-06-16 15:44:53,179 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-06-16 15:44:55,217 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 15:44:58,243 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-06-16 15:44:58,244 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 15:44:58,245 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-06-16 15:44:58,247 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-06-16 15:45:05,740 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-06-16 15:45:05,746 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-06-16 15:45:06,026 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-06-16 15:45:06,456 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:84)
2025-06-16 15:45:06,457 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-06-16 15:45:06,458 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-06-16 15:45:09,677 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-06-16 15:45:09,677 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 4 kullanıcı). (status_checker.py:84)
2025-06-16 15:45:12,957 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-06-16 15:45:17,365 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 4 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-06-16 15:45:20,772 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-06-16 15:45:22,035 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 4 (status_checker.py:84)
2025-06-16 15:45:22,038 [INFO] StatusChecker - [STATUS_CHECKER] 💾 4 kaydın durumu güncellendi. (status_checker.py:84)
2025-06-16 15:45:25,925 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-06-16 15:45:30,497 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-06-16 15:45:50,868 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-06-16 15:45:50,869 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-06-16 15:45:50,877 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:268)
2025-06-16 15:45:50,881 [INFO] main - ✅ Status Checker thread temizlendi (main.py:274)
2025-06-16 15:45:50,929 [INFO] main - 3️⃣ Message Sender başlatılıyor (1 kullanıcı) (main.py:286)
2025-06-16 15:45:50,929 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:433)
2025-06-16 15:48:12,384 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 15:48:12,384 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 15:48:12,394 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 15:48:12,411 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 15:48:12,411 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 15:57:54,266 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 15:57:54,267 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 15:57:57,486 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 15:57:57,486 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 15:58:01,768 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 15:58:01,769 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 15:58:01,770 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:206)
2025-06-16 15:58:01,772 [INFO] main - ✅ Otomasyon durduruldu (main.py:215)
2025-06-16 15:58:01,984 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 15:58:04,870 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 15:58:04,870 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 15:58:04,880 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 15:58:04,892 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 15:58:04,893 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 15:58:43,124 [INFO] __main__ - 📨 Komut: start (ID: 380) (automation_worker.py:214)
2025-06-16 15:58:43,124 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 15:58:46,293 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 15:58:46,293 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 15:58:46,293 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 15:58:46,296 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 15:58:46,296 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 15:58:46,297 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 15:58:46,297 [INFO] main - 🔧 start_scraper çağrıldı (main.py:323)
2025-06-16 15:58:46,297 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:332)
2025-06-16 15:58:46,298 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:335)
2025-06-16 15:58:46,298 [INFO] main - ✅ Scraper thread başlatıldı (main.py:349)
2025-06-16 15:58:46,302 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 380) (automation_worker.py:150)
2025-06-16 15:58:50,724 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-06-16 15:58:50,724 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-06-16 15:58:58,768 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-06-16 15:59:00,391 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-06-16 15:59:20,441 [INFO] scraper_thread - [SCRAPER] sinem6534 | 297 (scraper_thread.py:321)
2025-06-16 15:59:28,139 [INFO] scraper_thread - [SCRAPER] ezirayil0 | 0 (scraper_thread.py:321)
2025-06-16 15:59:32,701 [INFO] scraper_thread - [SCRAPER] turgayselvinaz.ozkan | 303 (scraper_thread.py:321)
2025-06-16 15:59:37,247 [INFO] scraper_thread - [SCRAPER] osmanzengin220 | 95 (scraper_thread.py:321)
2025-06-16 15:59:43,054 [INFO] scraper_thread - [SCRAPER] aboofatos | 11 (scraper_thread.py:321)
2025-06-16 15:59:47,961 [INFO] scraper_thread - [SCRAPER] sevdeyargimachine | 28 (scraper_thread.py:321)
2025-06-16 15:59:53,556 [INFO] scraper_thread - [SCRAPER] .murat.kl5 | 0 (scraper_thread.py:321)
2025-06-16 16:00:00,852 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 7 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-06-16 16:00:30,017 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-06-16 16:01:16,023 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-06-16 16:01:21,325 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:224)
2025-06-16 16:01:21,327 [INFO] main - ✅ Scraper thread temizlendi (main.py:230)
2025-06-16 16:01:21,331 [INFO] main - 🔍 Son 1 dakikada toplam 0 kullanıcı: (main.py:247)
2025-06-16 16:01:21,331 [INFO] main - 🔄 'Bekleniyor' statüsünde kullanıcı bulunamadı, scraper tekrar başlatılıyor (main.py:256)
2025-06-16 16:01:23,336 [INFO] main - 🔧 start_scraper çağrıldı (main.py:323)
2025-06-16 16:01:23,336 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:332)
2025-06-16 16:01:23,336 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:335)
2025-06-16 16:01:23,337 [INFO] main - ✅ Scraper thread başlatıldı (main.py:349)
2025-06-16 16:01:28,445 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-06-16 16:01:36,871 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-06-16 16:01:43,171 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-06-16 16:02:16,293 [INFO] scraper_thread - [SCRAPER] sinem6534 | 309 (scraper_thread.py:321)
2025-06-16 16:02:44,964 [INFO] scraper_thread - [SCRAPER] Süre doldu (1.0 dk) (scraper_thread.py:321)
2025-06-16 16:02:44,964 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 1 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-06-16 16:02:47,840 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-06-16 16:02:48,631 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:224)
2025-06-16 16:02:48,632 [INFO] main - ✅ Scraper thread temizlendi (main.py:230)
2025-06-16 16:02:48,637 [INFO] main - 🔍 Son 1 dakikada toplam 1 kullanıcı: (main.py:247)
2025-06-16 16:02:48,638 [INFO] main -   - sinem6534: Bekleniyor (2025-06-16 16:02:44) (main.py:249)
2025-06-16 16:02:48,639 [INFO] main - 2️⃣ Status Checker başlatılıyor (1 kullanıcı) (main.py:253)
2025-06-16 16:02:48,639 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:358)
2025-06-16 16:02:48,640 [INFO] main - 🔧 Phase lock alınıyor... (main.py:364)
2025-06-16 16:02:48,641 [INFO] main - 🔧 Phase lock alındı (main.py:366)
2025-06-16 16:02:48,641 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:374)
2025-06-16 16:02:48,642 [INFO] main - 2️⃣ Status Checker başlatılıyor (1 kullanıcı) (main.py:377)
2025-06-16 16:02:48,642 [INFO] main - 📋 Kullanıcılar: ['sinem6534'] (main.py:378)
2025-06-16 16:02:48,642 [INFO] main - 📋 Publishers formatı: [{'username': 'sinem6534'}] (main.py:382)
2025-06-16 16:02:48,643 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:384)
2025-06-16 16:02:48,643 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:394)
2025-06-16 16:02:48,644 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:395)
2025-06-16 16:02:48,644 [INFO] main - 🔧 Thread daemon: True (main.py:396)
2025-06-16 16:02:48,645 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:405)
2025-06-16 16:02:48,646 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-06-16 16:02:48,646 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:409)
2025-06-16 16:02:48,646 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 1 (status_checker.py:84)
2025-06-16 16:02:48,647 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:418)
2025-06-16 16:02:48,648 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 2676 (status_checker.py:84)
2025-06-16 16:02:48,648 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-06-16 16:02:48,649 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:02:48,649 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-06-16 16:02:48,650 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-06-16 16:02:51,836 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-06-16 16:02:51,836 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 1 kullanıcı işlenecek (status_checker.py:84)
2025-06-16 16:02:51,836 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'sinem6534'}] (status_checker.py:84)
2025-06-16 16:02:51,837 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-06-16 16:02:51,837 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-06-16 16:02:51,837 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-06-16 16:02:51,837 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-06-16 16:02:51,838 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-06-16 16:02:51,838 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-06-16 16:02:51,838 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-06-16 16:02:51,839 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-06-16 16:02:53,824 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:03:00,015 [INFO] main - 📊 Thread durumu: status_checker - Alive: True, Finished: False (main.py:126)
2025-06-16 16:03:02,267 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-06-16 16:03:02,267 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:03:02,268 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-06-16 16:03:02,269 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-06-16 16:03:07,831 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-06-16 16:03:07,831 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-06-16 16:03:08,417 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-06-16 16:03:08,890 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:84)
2025-06-16 16:03:08,891 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-06-16 16:03:08,892 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-06-16 16:03:12,146 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-06-16 16:03:12,146 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 1 kullanıcı). (status_checker.py:84)
2025-06-16 16:03:14,768 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-06-16 16:03:18,760 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 1 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-06-16 16:03:21,437 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-06-16 16:03:23,065 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 1 (status_checker.py:84)
2025-06-16 16:03:23,069 [INFO] StatusChecker - [STATUS_CHECKER] 💾 1 kaydın durumu güncellendi. (status_checker.py:84)
2025-06-16 16:03:26,569 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-06-16 16:03:30,256 [INFO] main - 📊 Thread durumu: status_checker - Alive: True, Finished: False (main.py:126)
2025-06-16 16:03:31,075 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-06-16 16:03:31,238 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-06-16 16:03:31,240 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-06-16 16:03:31,241 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:270)
2025-06-16 16:03:31,241 [INFO] main - ✅ Status Checker thread temizlendi (main.py:276)
2025-06-16 16:03:31,280 [INFO] main - 3️⃣ Message Sender başlatılıyor (1 kullanıcı) (main.py:288)
2025-06-16 16:03:31,280 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:445)
2025-06-16 16:03:31,283 [INFO] main - 🔧 start_scraper çağrıldı (main.py:323)
2025-06-16 16:05:17,259 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 16:05:17,259 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 16:05:17,269 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 16:05:17,282 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 16:05:17,282 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 16:06:04,429 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 16:06:04,431 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 16:06:04,642 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 16:06:08,258 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 16:06:08,258 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 16:06:08,259 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:206)
2025-06-16 16:06:08,260 [INFO] main - ✅ Otomasyon durduruldu (main.py:215)
2025-06-16 16:06:08,450 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 16:06:10,534 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 16:06:10,534 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 16:06:10,545 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 16:06:10,555 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 16:06:10,556 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 16:08:15,687 [INFO] __main__ - 📨 Komut: start (ID: 381) (automation_worker.py:214)
2025-06-16 16:08:18,555 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 16:08:21,809 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 16:08:21,809 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 16:08:21,810 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 16:08:21,813 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 16:08:21,813 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 16:08:21,814 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 16:08:21,814 [INFO] main - 🔧 start_scraper çağrıldı (main.py:323)
2025-06-16 16:08:21,814 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:332)
2025-06-16 16:08:21,814 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:335)
2025-06-16 16:08:21,815 [INFO] main - ✅ Scraper thread başlatıldı (main.py:349)
2025-06-16 16:08:21,819 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 381) (automation_worker.py:150)
2025-06-16 16:08:26,136 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-06-16 16:08:26,136 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-06-16 16:08:33,797 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-06-16 16:08:38,268 [INFO] scraper_thread - [SCRAPER] theege61 | 29 (scraper_thread.py:321)
2025-06-16 16:08:50,447 [INFO] scraper_thread - [SCRAPER] gulumser_konya_42 | 184 (scraper_thread.py:321)
2025-06-16 16:08:55,123 [INFO] scraper_thread - [SCRAPER] thesefa99 | 17 (scraper_thread.py:321)
2025-06-16 16:08:58,988 [INFO] scraper_thread - [SCRAPER] antepli.63 | 16 (scraper_thread.py:321)
2025-06-16 16:09:00,133 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-06-16 16:09:04,407 [INFO] scraper_thread - [SCRAPER] saturnenesbs | 4 (scraper_thread.py:321)
2025-06-16 16:09:09,040 [INFO] scraper_thread - [SCRAPER] ahmetagirmann | 309 (scraper_thread.py:321)
2025-06-16 16:09:13,247 [INFO] scraper_thread - [SCRAPER] elinags1905 | 22 (scraper_thread.py:321)
2025-06-16 16:09:17,583 [INFO] scraper_thread - [SCRAPER] keniverse0 | 13 (scraper_thread.py:321)
2025-06-16 16:09:22,091 [INFO] scraper_thread - [SCRAPER] lilloos9 | 57 (scraper_thread.py:321)
2025-06-16 16:09:26,413 [INFO] scraper_thread - [SCRAPER] _aleynaileumut | 14 (scraper_thread.py:321)
2025-06-16 16:09:30,335 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-06-16 16:09:32,116 [INFO] scraper_thread - [SCRAPER] zeynosfer | 31 (scraper_thread.py:321)
2025-06-16 16:09:36,602 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 11 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-06-16 16:09:39,269 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-06-16 16:09:39,269 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:224)
2025-06-16 16:09:39,270 [INFO] main - ✅ Scraper thread temizlendi (main.py:230)
2025-06-16 16:09:39,272 [INFO] main - 🔍 Son 1 dakikada toplam 10 kullanıcı: (main.py:247)
2025-06-16 16:09:39,272 [INFO] main -   - zeynosfer: Bekleniyor (2025-06-16 16:09:32) (main.py:249)
2025-06-16 16:09:39,273 [INFO] main -   - _aleynaileumut: Bekleniyor (2025-06-16 16:09:26) (main.py:249)
2025-06-16 16:09:39,273 [INFO] main -   - lilloos9: Bekleniyor (2025-06-16 16:09:22) (main.py:249)
2025-06-16 16:09:39,274 [INFO] main -   - keniverse0: Bekleniyor (2025-06-16 16:09:17) (main.py:249)
2025-06-16 16:09:39,274 [INFO] main -   - elinags1905: Bekleniyor (2025-06-16 16:09:13) (main.py:249)
2025-06-16 16:09:39,275 [INFO] main -   - ahmetagirmann: Bekleniyor (2025-06-16 16:09:09) (main.py:249)
2025-06-16 16:09:39,275 [INFO] main -   - saturnenesbs: Bekleniyor (2025-06-16 16:09:04) (main.py:249)
2025-06-16 16:09:39,275 [INFO] main -   - antepli.63: Bekleniyor (2025-06-16 16:08:58) (main.py:249)
2025-06-16 16:09:39,276 [INFO] main -   - thesefa99: Bekleniyor (2025-06-16 16:08:55) (main.py:249)
2025-06-16 16:09:39,276 [INFO] main -   - gulumser_konya_42: Bekleniyor (2025-06-16 16:08:50) (main.py:249)
2025-06-16 16:09:39,277 [INFO] main - 2️⃣ Status Checker başlatılıyor (10 kullanıcı) (main.py:253)
2025-06-16 16:09:39,277 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:358)
2025-06-16 16:09:39,278 [INFO] main - 🔧 Phase lock alınıyor... (main.py:364)
2025-06-16 16:09:39,278 [INFO] main - 🔧 Phase lock alındı (main.py:366)
2025-06-16 16:09:39,278 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:374)
2025-06-16 16:09:39,279 [INFO] main - 2️⃣ Status Checker başlatılıyor (10 kullanıcı) (main.py:377)
2025-06-16 16:09:39,279 [INFO] main - 📋 Kullanıcılar: ['gulumser_konya_42', 'thesefa99', 'antepli.63', 'saturnenesbs', 'ahmetagirmann', 'elinags1905', 'keniverse0', 'lilloos9', '_aleynaileumut', 'zeynosfer'] (main.py:378)
2025-06-16 16:09:39,280 [INFO] main - 📋 Publishers formatı: [{'username': 'gulumser_konya_42'}, {'username': 'thesefa99'}, {'username': 'antepli.63'}, {'username': 'saturnenesbs'}, {'username': 'ahmetagirmann'}, {'username': 'elinags1905'}, {'username': 'keniverse0'}, {'username': 'lilloos9'}, {'username': '_aleynaileumut'}, {'username': 'zeynosfer'}] (main.py:382)
2025-06-16 16:09:39,281 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:384)
2025-06-16 16:09:39,282 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:394)
2025-06-16 16:09:39,282 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:395)
2025-06-16 16:09:39,283 [INFO] main - 🔧 Thread daemon: True (main.py:396)
2025-06-16 16:09:39,283 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:405)
2025-06-16 16:09:39,284 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-06-16 16:09:39,284 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:409)
2025-06-16 16:09:39,284 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 10 (status_checker.py:84)
2025-06-16 16:09:39,287 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 12444 (status_checker.py:84)
2025-06-16 16:09:39,288 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:418)
2025-06-16 16:09:39,288 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-06-16 16:09:39,289 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:09:39,290 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-06-16 16:09:39,290 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-06-16 16:09:42,548 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-06-16 16:09:42,548 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 10 kullanıcı işlenecek (status_checker.py:84)
2025-06-16 16:09:42,550 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'gulumser_konya_42'}, {'username': 'thesefa99'}, {'username': 'antepli.63'}] (status_checker.py:84)
2025-06-16 16:09:42,550 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-06-16 16:09:42,551 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-06-16 16:09:42,551 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-06-16 16:09:42,553 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-06-16 16:09:42,553 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-06-16 16:09:42,553 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-06-16 16:09:42,553 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-06-16 16:09:42,554 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-06-16 16:09:44,448 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:09:47,468 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-06-16 16:09:47,468 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:09:47,469 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-06-16 16:09:47,471 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-06-16 16:09:48,504 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-06-16 16:09:48,506 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-06-16 16:09:50,466 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-06-16 16:09:50,480 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-06-16 16:09:50,480 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-06-16 16:09:50,481 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-06-16 16:09:53,552 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-06-16 16:09:53,556 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 10 kullanıcı). (status_checker.py:84)
2025-06-16 16:09:57,295 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-06-16 16:10:00,555 [INFO] main - 📊 Thread durumu: status_checker - Alive: True, Finished: False (main.py:126)
2025-06-16 16:10:02,492 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 10 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-06-16 16:10:05,847 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-06-16 16:10:07,328 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 10 (status_checker.py:84)
2025-06-16 16:10:07,333 [INFO] StatusChecker - [STATUS_CHECKER] 💾 10 kaydın durumu güncellendi. (status_checker.py:84)
2025-06-16 16:10:10,164 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-06-16 16:10:15,764 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-06-16 16:10:15,921 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-06-16 16:10:15,922 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-06-16 16:10:15,923 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:270)
2025-06-16 16:10:15,924 [INFO] main - ✅ Status Checker thread temizlendi (main.py:276)
2025-06-16 16:10:15,961 [INFO] main - 3️⃣ Message Sender başlatılıyor (4 kullanıcı) (main.py:288)
2025-06-16 16:10:15,961 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:445)
2025-06-16 16:20:13,408 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 16:20:13,409 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 16:20:13,420 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 16:20:13,431 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 16:20:13,431 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 16:21:44,634 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 16:21:44,634 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 16:21:44,644 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 16:21:44,654 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 16:21:44,656 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 16:23:52,295 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 16:23:52,295 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 16:23:52,295 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:206)
2025-06-16 16:23:52,295 [INFO] main - ✅ Otomasyon durduruldu (main.py:215)
2025-06-16 16:23:52,452 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 16:23:56,655 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 16:23:56,717 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 16:23:56,748 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 16:23:56,748 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 16:23:56,748 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
2025-06-16 16:25:51,233 [INFO] __main__ - 📨 Komut: start (ID: 382) (automation_worker.py:214)
2025-06-16 16:25:52,405 [INFO] __main__ - 📨 Komut: start (ID: 382) (automation_worker.py:214)
2025-06-16 16:25:52,405 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 16:25:52,405 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 16:25:55,623 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 16:25:55,623 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 16:25:55,623 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 16:25:55,623 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 16:25:55,623 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 16:25:55,623 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 16:25:55,623 [INFO] main - 🔧 start_scraper çağrıldı (main.py:323)
2025-06-16 16:25:55,623 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:325)
2025-06-16 16:25:55,623 [INFO] main - 🔧 Phase lock alındı (main.py:328)
2025-06-16 16:25:55,623 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:345)
2025-06-16 16:25:55,623 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:348)
2025-06-16 16:25:55,623 [INFO] main - ✅ Scraper thread başlatıldı (main.py:362)
2025-06-16 16:25:55,623 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 382) (automation_worker.py:150)
2025-06-16 16:25:55,660 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 16:25:55,680 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 16:25:55,680 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 16:25:55,683 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 16:25:55,684 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 16:25:55,685 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 16:25:55,685 [INFO] main - 🔧 start_scraper çağrıldı (main.py:323)
2025-06-16 16:25:55,688 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:325)
2025-06-16 16:25:55,688 [INFO] main - 🔧 Phase lock alındı (main.py:328)
2025-06-16 16:25:55,688 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:345)
2025-06-16 16:25:55,689 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:348)
2025-06-16 16:25:55,690 [INFO] main - ✅ Scraper thread başlatıldı (main.py:362)
2025-06-16 16:25:55,695 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 382) (automation_worker.py:150)
2025-06-16 16:26:00,933 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-06-16 16:26:00,933 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-06-16 16:26:04,169 [WARNING] scraper_thread - [SCRAPER] ⚠️ Bot algılama önlemleri uygulanamadı: Message: javascript error: Cannot redefine property: webdriver
  (Session info: chrome=137.0.7151.68)
Stacktrace:
	GetHandleVerifier [0x0x7ff6555dfea5+79173]
	GetHandleVerifier [0x0x7ff6555dff00+79264]
	(No symbol) [0x0x7ff655399e5a]
	(No symbol) [0x0x7ff6553a184d]
	(No symbol) [0x0x7ff6553a4be1]
	(No symbol) [0x0x7ff6554422b4]
	(No symbol) [0x0x7ff65541896a]
	(No symbol) [0x0x7ff65544100d]
	(No symbol) [0x0x7ff655418743]
	(No symbol) [0x0x7ff6553e14c1]
	(No symbol) [0x0x7ff6553e2253]
	GetHandleVerifier [0x0x7ff6558aa2dd+3004797]
	GetHandleVerifier [0x0x7ff6558a472d+2981325]
	GetHandleVerifier [0x0x7ff6558c3380+3107360]
	GetHandleVerifier [0x0x7ff6555faa2e+188622]
	GetHandleVerifier [0x0x7ff6556022bf+219487]
	GetHandleVerifier [0x0x7ff6555e8df4+115860]
	GetHandleVerifier [0x0x7ff6555e8fa9+116297]
	GetHandleVerifier [0x0x7ff6555cf558+11256]
	BaseThreadInitThunk [0x0x7ff80e937374+20]
	RtlUserThreadStart [0x0x7ff81081cc91+33]
 (scraper_thread.py:323)
2025-06-16 16:26:04,197 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-06-16 16:26:09,471 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-06-16 16:26:09,483 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-06-16 16:26:16,136 [INFO] scraper_thread - [SCRAPER] theege61 | 92 (scraper_thread.py:321)
2025-06-16 16:26:16,137 [INFO] scraper_thread - [SCRAPER] theege61 | 92 (scraper_thread.py:321)
2025-06-16 16:26:20,392 [INFO] scraper_thread - [SCRAPER] woxbultt | 12 (scraper_thread.py:321)
2025-06-16 16:26:22,610 [INFO] scraper_thread - [SCRAPER] ayenerjisi | 7 (scraper_thread.py:321)
2025-06-16 16:26:24,943 [INFO] scraper_thread - [SCRAPER] aboofatos | 14 (scraper_thread.py:321)
2025-06-16 16:26:27,488 [INFO] scraper_thread - [SCRAPER] armenv4 | 14 (scraper_thread.py:321)
2025-06-16 16:26:29,494 [INFO] scraper_thread - [SCRAPER] bariton | 92 (scraper_thread.py:321)
2025-06-16 16:26:31,886 [INFO] scraper_thread - [SCRAPER] hurdaci01.34 | 190 (scraper_thread.py:321)
2025-06-16 16:26:40,249 [INFO] scraper_thread - [SCRAPER] gulumser_konya_42 | 0 (scraper_thread.py:321)
2025-06-16 16:26:43,178 [INFO] scraper_thread - [SCRAPER] gulumser_konya_42 | 18 (scraper_thread.py:321)
2025-06-16 16:26:45,541 [INFO] scraper_thread - [SCRAPER] dilaysh.n | 4 (scraper_thread.py:321)
2025-06-16 16:26:48,415 [INFO] scraper_thread - [SCRAPER] 1melikeferhat | 2 (scraper_thread.py:321)
2025-06-16 16:26:51,294 [INFO] scraper_thread - [SCRAPER] bestoollii | 23 (scraper_thread.py:321)
2025-06-16 16:26:53,143 [INFO] scraper_thread - [SCRAPER] rupertapm | 821 (scraper_thread.py:321)
2025-06-16 16:26:56,769 [INFO] scraper_thread - [SCRAPER] .krdln_ | 26 (scraper_thread.py:321)
2025-06-16 16:26:58,709 [INFO] scraper_thread - [SCRAPER] mustafa_gok04 | 2 (scraper_thread.py:321)
2025-06-16 16:27:00,064 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-06-16 16:27:00,205 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-06-16 16:27:01,938 [INFO] scraper_thread - [SCRAPER] 06eskal | 2 (scraper_thread.py:321)
2025-06-16 16:27:04,263 [INFO] scraper_thread - [SCRAPER] _hmody_511 | 6 (scraper_thread.py:321)
2025-06-16 16:27:06,123 [INFO] scraper_thread - [SCRAPER] modga.pm | 239 (scraper_thread.py:321)
2025-06-16 16:27:09,734 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 9 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-06-16 16:27:10,526 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 10 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-06-16 16:27:13,561 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-06-16 16:27:13,561 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:224)
2025-06-16 16:27:13,561 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:224)
2025-06-16 16:27:13,561 [INFO] main - ✅ Scraper thread temizlendi (main.py:230)
2025-06-16 16:27:13,561 [INFO] main - ✅ Scraper thread temizlendi (main.py:230)
2025-06-16 16:27:13,561 [INFO] main - 🔍 Son 1 dakikada toplam 17 kullanıcı: (main.py:247)
2025-06-16 16:27:13,561 [INFO] main - 🔍 Son 1 dakikada toplam 17 kullanıcı: (main.py:247)
2025-06-16 16:27:13,561 [INFO] main -   - modga.pm: Bekleniyor (2025-06-16 16:27:06) (main.py:249)
2025-06-16 16:27:13,561 [INFO] main -   - modga.pm: Bekleniyor (2025-06-16 16:27:06) (main.py:249)
2025-06-16 16:27:13,561 [INFO] main -   - _hmody_511: Bekleniyor (2025-06-16 16:27:04) (main.py:249)
2025-06-16 16:27:13,561 [INFO] main -   - _hmody_511: Bekleniyor (2025-06-16 16:27:04) (main.py:249)
2025-06-16 16:27:13,561 [INFO] main -   - 06eskal: Bekleniyor (2025-06-16 16:27:01) (main.py:249)
2025-06-16 16:27:13,561 [INFO] main -   - mustafa_gok04: Bekleniyor (2025-06-16 16:26:58) (main.py:249)
2025-06-16 16:27:13,561 [INFO] main -   - .krdln_: Bekleniyor (2025-06-16 16:26:56) (main.py:249)
2025-06-16 16:27:13,561 [INFO] main -   - 06eskal: Bekleniyor (2025-06-16 16:27:01) (main.py:249)
2025-06-16 16:27:13,561 [INFO] main -   - rupertapm: Bekleniyor (2025-06-16 16:26:53) (main.py:249)
2025-06-16 16:27:13,561 [INFO] main -   - bestoollii: Bekleniyor (2025-06-16 16:26:51) (main.py:249)
2025-06-16 16:27:13,561 [INFO] main -   - 1melikeferhat: Bekleniyor (2025-06-16 16:26:48) (main.py:249)
2025-06-16 16:27:13,561 [INFO] main -   - dilaysh.n: Bekleniyor (2025-06-16 16:26:45) (main.py:249)
2025-06-16 16:27:13,561 [INFO] main -   - gulumser_konya_42: Bekleniyor (2025-06-16 16:26:43) (main.py:249)
2025-06-16 16:27:13,561 [INFO] main -   - hurdaci01.34: Bekleniyor (2025-06-16 16:26:31) (main.py:249)
2025-06-16 16:27:13,561 [INFO] main -   - bariton: Bekleniyor (2025-06-16 16:26:29) (main.py:249)
2025-06-16 16:27:13,570 [INFO] main -   - armenv4: Bekleniyor (2025-06-16 16:26:27) (main.py:249)
2025-06-16 16:27:13,570 [INFO] main -   - aboofatos: Bekleniyor (2025-06-16 16:26:24) (main.py:249)
2025-06-16 16:27:13,571 [INFO] main -   - ayenerjisi: Bekleniyor (2025-06-16 16:26:22) (main.py:249)
2025-06-16 16:27:13,571 [INFO] main -   - woxbultt: Bekleniyor (2025-06-16 16:26:20) (main.py:249)
2025-06-16 16:27:13,571 [INFO] main -   - theege61: Bekleniyor (2025-06-16 16:26:16) (main.py:249)
2025-06-16 16:27:13,572 [INFO] main - 2️⃣ Status Checker başlatılıyor (17 kullanıcı) (main.py:253)
2025-06-16 16:27:13,572 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:371)
2025-06-16 16:27:13,573 [INFO] main - 🔧 Phase lock alınıyor... (main.py:377)
2025-06-16 16:27:13,573 [INFO] main - 🔧 Phase lock alındı (main.py:379)
2025-06-16 16:27:13,573 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:387)
2025-06-16 16:27:13,574 [INFO] main - 2️⃣ Status Checker başlatılıyor (17 kullanıcı) (main.py:390)
2025-06-16 16:27:13,574 [INFO] main - 📋 Kullanıcılar: ['theege61', 'woxbultt', 'ayenerjisi', 'aboofatos', 'armenv4', 'bariton', 'hurdaci01.34', 'gulumser_konya_42', 'dilaysh.n', '1melikeferhat', 'bestoollii', 'rupertapm', '.krdln_', 'mustafa_gok04', '06eskal', '_hmody_511', 'modga.pm'] (main.py:391)
2025-06-16 16:27:13,575 [INFO] main - 📋 Publishers formatı: [{'username': 'theege61'}, {'username': 'woxbultt'}, {'username': 'ayenerjisi'}, {'username': 'aboofatos'}, {'username': 'armenv4'}, {'username': 'bariton'}, {'username': 'hurdaci01.34'}, {'username': 'gulumser_konya_42'}, {'username': 'dilaysh.n'}, {'username': '1melikeferhat'}, {'username': 'bestoollii'}, {'username': 'rupertapm'}, {'username': '.krdln_'}, {'username': 'mustafa_gok04'}, {'username': '06eskal'}, {'username': '_hmody_511'}, {'username': 'modga.pm'}] (main.py:395)
2025-06-16 16:27:13,576 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:397)
2025-06-16 16:27:13,577 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:407)
2025-06-16 16:27:13,578 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:408)
2025-06-16 16:27:13,578 [INFO] main - 🔧 Thread daemon: True (main.py:409)
2025-06-16 16:27:13,579 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:418)
2025-06-16 16:27:13,582 [INFO] main -   - mustafa_gok04: Bekleniyor (2025-06-16 16:26:58) (main.py:249)
2025-06-16 16:27:13,582 [INFO] main -   - .krdln_: Bekleniyor (2025-06-16 16:26:56) (main.py:249)
2025-06-16 16:27:13,583 [INFO] main -   - rupertapm: Bekleniyor (2025-06-16 16:26:53) (main.py:249)
2025-06-16 16:27:13,583 [INFO] main -   - bestoollii: Bekleniyor (2025-06-16 16:26:51) (main.py:249)
2025-06-16 16:27:13,595 [INFO] main -   - 1melikeferhat: Bekleniyor (2025-06-16 16:26:48) (main.py:249)
2025-06-16 16:27:13,596 [INFO] main -   - dilaysh.n: Bekleniyor (2025-06-16 16:26:45) (main.py:249)
2025-06-16 16:27:13,596 [INFO] main -   - gulumser_konya_42: Bekleniyor (2025-06-16 16:26:43) (main.py:249)
2025-06-16 16:27:13,597 [INFO] main -   - hurdaci01.34: Bekleniyor (2025-06-16 16:26:31) (main.py:249)
2025-06-16 16:27:13,597 [INFO] main -   - bariton: Bekleniyor (2025-06-16 16:26:29) (main.py:249)
2025-06-16 16:27:13,598 [INFO] main -   - armenv4: Bekleniyor (2025-06-16 16:26:27) (main.py:249)
2025-06-16 16:27:13,598 [INFO] main -   - aboofatos: Bekleniyor (2025-06-16 16:26:24) (main.py:249)
2025-06-16 16:27:13,599 [INFO] main -   - ayenerjisi: Bekleniyor (2025-06-16 16:26:22) (main.py:249)
2025-06-16 16:27:13,600 [INFO] main -   - woxbultt: Bekleniyor (2025-06-16 16:26:20) (main.py:249)
2025-06-16 16:27:13,601 [INFO] main -   - theege61: Bekleniyor (2025-06-16 16:26:16) (main.py:249)
2025-06-16 16:27:13,601 [INFO] main - 2️⃣ Status Checker başlatılıyor (17 kullanıcı) (main.py:253)
2025-06-16 16:27:13,602 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:371)
2025-06-16 16:27:13,603 [INFO] main - 🔧 Phase lock alınıyor... (main.py:377)
2025-06-16 16:27:13,603 [INFO] main - 🔧 Phase lock alındı (main.py:379)
2025-06-16 16:27:13,604 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:387)
2025-06-16 16:27:13,604 [INFO] main - 2️⃣ Status Checker başlatılıyor (17 kullanıcı) (main.py:390)
2025-06-16 16:27:13,605 [INFO] main - 📋 Kullanıcılar: ['theege61', 'woxbultt', 'ayenerjisi', 'aboofatos', 'armenv4', 'bariton', 'hurdaci01.34', 'gulumser_konya_42', 'dilaysh.n', '1melikeferhat', 'bestoollii', 'rupertapm', '.krdln_', 'mustafa_gok04', '06eskal', '_hmody_511', 'modga.pm'] (main.py:391)
2025-06-16 16:27:13,616 [INFO] main - 📋 Publishers formatı: [{'username': 'theege61'}, {'username': 'woxbultt'}, {'username': 'ayenerjisi'}, {'username': 'aboofatos'}, {'username': 'armenv4'}, {'username': 'bariton'}, {'username': 'hurdaci01.34'}, {'username': 'gulumser_konya_42'}, {'username': 'dilaysh.n'}, {'username': '1melikeferhat'}, {'username': 'bestoollii'}, {'username': 'rupertapm'}, {'username': '.krdln_'}, {'username': 'mustafa_gok04'}, {'username': '06eskal'}, {'username': '_hmody_511'}, {'username': 'modga.pm'}] (main.py:395)
2025-06-16 16:27:13,618 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:397)
2025-06-16 16:27:13,619 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:407)
2025-06-16 16:27:13,619 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:408)
2025-06-16 16:27:13,620 [INFO] main - 🔧 Thread daemon: True (main.py:409)
2025-06-16 16:27:13,620 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:418)
2025-06-16 16:27:13,669 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-06-16 16:27:13,669 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:422)
2025-06-16 16:27:13,670 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 17 (status_checker.py:84)
2025-06-16 16:27:13,671 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:431)
2025-06-16 16:27:13,673 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-06-16 16:27:13,673 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:422)
2025-06-16 16:27:13,673 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 7864 (status_checker.py:84)
2025-06-16 16:27:13,679 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-06-16 16:27:13,679 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 17 (status_checker.py:84)
2025-06-16 16:27:13,679 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:431)
2025-06-16 16:27:13,679 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:27:13,680 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 9864 (status_checker.py:84)
2025-06-16 16:27:13,680 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-06-16 16:27:13,682 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-06-16 16:27:13,682 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-06-16 16:27:13,685 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:27:13,685 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-06-16 16:27:13,692 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-06-16 16:27:16,951 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-06-16 16:27:16,951 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 17 kullanıcı işlenecek (status_checker.py:84)
2025-06-16 16:27:16,951 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'theege61'}, {'username': 'woxbultt'}, {'username': 'ayenerjisi'}] (status_checker.py:84)
2025-06-16 16:27:16,951 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 17 kullanıcı işlenecek (status_checker.py:84)
2025-06-16 16:27:16,951 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-06-16 16:27:16,951 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'theege61'}, {'username': 'woxbultt'}, {'username': 'ayenerjisi'}] (status_checker.py:84)
2025-06-16 16:27:16,951 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-06-16 16:27:16,951 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-06-16 16:27:16,951 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-06-16 16:27:16,951 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-06-16 16:27:16,951 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-06-16 16:27:16,951 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-06-16 16:27:16,951 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-06-16 16:27:16,951 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)

2025-06-16 16:27:16,951 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-06-16 16:27:16,951 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-06-16 16:27:16,967 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-06-16 16:27:16,969 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-06-16 16:27:16,971 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-06-16 16:27:16,976 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-06-16 16:27:19,556 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:27:19,558 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:27:22,618 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-06-16 16:27:22,618 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:27:22,619 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-06-16 16:27:22,621 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-06-16 16:27:22,654 [WARNING] StatusChecker - [STATUS_CHECKER] ⚠️ Bot algılama önlemleri uygulanamadı: Message: javascript error: Cannot redefine property: webdriver
  (Session info: chrome=137.0.7151.68)
Stacktrace:
	GetHandleVerifier [0x0x7ff79df2fea5+79173]
	GetHandleVerifier [0x0x7ff79df2ff00+79264]
	(No symbol) [0x0x7ff79dce9e5a]
	(No symbol) [0x0x7ff79dcf184d]
	(No symbol) [0x0x7ff79dcf4be1]
	(No symbol) [0x0x7ff79dd922b4]
	(No symbol) [0x0x7ff79dd6896a]
	(No symbol) [0x0x7ff79dd9100d]
	(No symbol) [0x0x7ff79dd68743]
	(No symbol) [0x0x7ff79dd314c1]
	(No symbol) [0x0x7ff79dd32253]
	GetHandleVerifier [0x0x7ff79e1fa2dd+3004797]
	GetHandleVerifier [0x0x7ff79e1f472d+2981325]
	GetHandleVerifier [0x0x7ff79e213380+3107360]
	GetHandleVerifier [0x0x7ff79df4aa2e+188622]
	GetHandleVerifier [0x0x7ff79df522bf+219487]
	GetHandleVerifier [0x0x7ff79df38df4+115860]
	GetHandleVerifier [0x0x7ff79df38fa9+116297]
	GetHandleVerifier [0x0x7ff79df1f558+11256]
	BaseThreadInitThunk [0x0x7ff80e937374+20]
	RtlUserThreadStart [0x0x7ff81081cc91+33]
 (status_checker.py:86)
2025-06-16 16:27:22,657 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:27:22,657 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-06-16 16:27:22,659 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-06-16 16:27:24,934 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-06-16 16:27:24,943 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-06-16 16:27:30,266 [INFO] main - 📊 Thread durumu: status_checker - Alive: True, Finished: False (main.py:126)
2025-06-16 16:27:30,535 [INFO] main - 📊 Thread durumu: status_checker - Alive: True, Finished: False (main.py:126)
2025-06-16 16:27:37,558 [INFO] __main__ - ⏹️ Program durduruldu (automation_worker.py:267)
2025-06-16 16:27:37,559 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 16:27:37,559 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:206)
2025-06-16 16:27:37,559 [INFO] StatusChecker - [STATUS_CHECKER] ⏹ StatusChecker durdurma sinyali alındı. (status_checker.py:84)
2025-06-16 16:27:37,560 [INFO] main - ✅ Otomasyon durduruldu (main.py:215)
2025-06-16 16:27:38,163 [INFO] __main__ - 🔚 Program sonlandırıldı (automation_worker.py:275)
2025-06-16 16:27:38,579 [INFO] main - 🔍 Monitör durduruldu (main.py:197)
2025-06-16 16:27:55,944 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 16:27:59,655 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 16:28:00,061 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 16:28:00,061 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 16:28:00,077 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 16:28:00,077 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 16:28:00,077 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 16:28:00,077 [INFO] main - 🔧 start_scraper çağrıldı (main.py:323)
2025-06-16 16:28:00,077 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:325)
2025-06-16 16:28:00,077 [INFO] main - 🔧 Phase lock alındı (main.py:328)
2025-06-16 16:28:00,077 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:345)
2025-06-16 16:28:00,077 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:348)
2025-06-16 16:28:00,077 [INFO] main - ✅ Scraper thread başlatıldı (main.py:362)
2025-06-16 16:28:00,077 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 382) (automation_worker.py:150)
2025-06-16 16:28:07,350 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-06-16 16:28:07,350 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-06-16 16:28:18,730 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-06-16 16:28:25,145 [INFO] scraper_thread - [SCRAPER] theege61 | 103 (scraper_thread.py:321)
2025-06-16 16:28:30,268 [INFO] scraper_thread - [SCRAPER] omerfaydin | 3 (scraper_thread.py:321)
2025-06-16 16:28:30,288 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-06-16 16:28:36,679 [INFO] scraper_thread - [SCRAPER] ebocuk | 10 (scraper_thread.py:321)
2025-06-16 16:28:42,024 [INFO] scraper_thread - [SCRAPER] ycfatih | 23 (scraper_thread.py:321)
2025-06-16 16:28:46,181 [INFO] scraper_thread - [SCRAPER] tt.bal.pubg | 76 (scraper_thread.py:321)
2025-06-16 16:28:50,665 [INFO] scraper_thread - [SCRAPER] bloxworld_03 | 9 (scraper_thread.py:321)
2025-06-16 16:28:56,248 [INFO] scraper_thread - [SCRAPER] feelgoodmanager3 | 165 (scraper_thread.py:321)
2025-06-16 16:29:00,237 [INFO] scraper_thread - [SCRAPER] 00.turkuaz | 4 (scraper_thread.py:321)
2025-06-16 16:29:00,493 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-06-16 16:29:05,732 [INFO] scraper_thread - [SCRAPER] thesefa99 | 12 (scraper_thread.py:321)
2025-06-16 16:29:19,952 [INFO] scraper_thread - [SCRAPER] can_obx3 | 9 (scraper_thread.py:321)
2025-06-16 16:29:44,747 [INFO] scraper_thread - [SCRAPER] Süre doldu (1.0 dk) (scraper_thread.py:321)
2025-06-16 16:29:44,747 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 10 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-06-16 16:29:47,420 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-06-16 16:29:47,420 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:224)
2025-06-16 16:29:47,421 [INFO] main - ✅ Scraper thread temizlendi (main.py:230)
2025-06-16 16:29:47,421 [INFO] main - 🔍 Son 1 dakikada toplam 5 kullanıcı: (main.py:247)
2025-06-16 16:29:47,421 [INFO] main -   - can_obx3: Bekleniyor (2025-06-16 16:29:44) (main.py:249)
2025-06-16 16:29:47,421 [INFO] main -   - thesefa99: Bekleniyor (2025-06-16 16:29:07) (main.py:249)
2025-06-16 16:29:47,421 [INFO] main -   - 00.turkuaz: Bekleniyor (2025-06-16 16:29:00) (main.py:249)
2025-06-16 16:29:47,421 [INFO] main -   - feelgoodmanager3: Bekleniyor (2025-06-16 16:28:56) (main.py:249)
2025-06-16 16:29:47,421 [INFO] main -   - bloxworld_03: Bekleniyor (2025-06-16 16:28:50) (main.py:249)
2025-06-16 16:29:47,421 [INFO] main - 2️⃣ Status Checker başlatılıyor (5 kullanıcı) (main.py:253)
2025-06-16 16:29:47,421 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:371)
2025-06-16 16:29:47,421 [INFO] main - 🔧 Phase lock alınıyor... (main.py:377)
2025-06-16 16:29:47,421 [INFO] main - 🔧 Phase lock alındı (main.py:379)
2025-06-16 16:29:47,421 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:387)
2025-06-16 16:29:47,421 [INFO] main - 2️⃣ Status Checker başlatılıyor (5 kullanıcı) (main.py:390)
2025-06-16 16:29:47,421 [INFO] main - 📋 Kullanıcılar: ['bloxworld_03', 'feelgoodmanager3', '00.turkuaz', 'thesefa99', 'can_obx3'] (main.py:391)
2025-06-16 16:29:47,421 [INFO] main - 📋 Publishers formatı: [{'username': 'bloxworld_03'}, {'username': 'feelgoodmanager3'}, {'username': '00.turkuaz'}, {'username': 'thesefa99'}, {'username': 'can_obx3'}] (main.py:395)
2025-06-16 16:29:47,421 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:397)
2025-06-16 16:29:47,421 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:407)
2025-06-16 16:29:47,421 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:408)
2025-06-16 16:29:47,421 [INFO] main - 🔧 Thread daemon: True (main.py:409)
2025-06-16 16:29:47,421 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:418)
2025-06-16 16:29:47,421 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-06-16 16:29:47,421 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:422)
2025-06-16 16:29:47,421 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 5 (status_checker.py:84)
2025-06-16 16:29:47,421 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:431)
2025-06-16 16:29:47,421 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 4332 (status_checker.py:84)
2025-06-16 16:29:47,421 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-06-16 16:29:47,421 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:29:47,421 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-06-16 16:29:47,421 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-06-16 16:29:50,623 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-06-16 16:29:50,623 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 5 kullanıcı işlenecek (status_checker.py:84)
2025-06-16 16:29:50,623 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'bloxworld_03'}, {'username': 'feelgoodmanager3'}, {'username': '00.turkuaz'}] (status_checker.py:84)
2025-06-16 16:29:50,623 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-06-16 16:29:50,623 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-06-16 16:29:50,623 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-06-16 16:29:50,623 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-06-16 16:29:50,623 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-06-16 16:29:50,623 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-06-16 16:29:50,623 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-06-16 16:29:50,623 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-06-16 16:29:53,191 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:29:56,227 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-06-16 16:29:56,227 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:29:56,229 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-06-16 16:29:56,231 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-06-16 16:29:58,965 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-06-16 16:29:58,965 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-06-16 16:29:58,987 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-06-16 16:29:58,995 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-06-16 16:29:58,995 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-06-16 16:29:58,996 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-06-16 16:30:02,225 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-06-16 16:30:02,229 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 5 kullanıcı). (status_checker.py:84)
2025-06-16 16:30:07,187 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-06-16 16:30:12,143 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 5 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-06-16 16:30:15,975 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-06-16 16:30:17,117 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 5 (status_checker.py:84)
2025-06-16 16:30:17,124 [INFO] StatusChecker - [STATUS_CHECKER] 💾 5 kaydın durumu güncellendi. (status_checker.py:84)
2025-06-16 16:30:20,228 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-06-16 16:30:24,780 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-06-16 16:30:24,936 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-06-16 16:30:24,936 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-06-16 16:30:24,936 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:270)
2025-06-16 16:30:24,936 [INFO] main - ✅ Status Checker thread temizlendi (main.py:276)
2025-06-16 16:30:24,967 [INFO] main - 3️⃣ Message Sender başlatılıyor (2 kullanıcı) (main.py:288)
2025-06-16 16:30:24,967 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:452)
2025-06-16 16:30:24,967 [INFO] main - ✅ Thread temizlendi, scraper başlatılıyor (main.py:457)
2025-06-16 16:30:25,983 [INFO] main - 🔧 start_scraper çağrıldı (main.py:323)
2025-06-16 16:30:25,983 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:325)
2025-06-16 16:30:25,983 [INFO] main - 🔧 Phase lock alındı (main.py:328)
2025-06-16 16:30:25,983 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:345)
2025-06-16 16:30:25,983 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:348)
2025-06-16 16:30:25,983 [INFO] main - ✅ Scraper thread başlatıldı (main.py:362)
2025-06-16 16:30:30,440 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-06-16 16:30:30,441 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-06-16 16:30:37,520 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-06-16 16:30:51,244 [INFO] scraper_thread - [SCRAPER] theege61 | 0 (scraper_thread.py:321)
2025-06-16 16:31:00,781 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-06-16 16:31:08,080 [INFO] scraper_thread - [SCRAPER] keniverse0 | 6 (scraper_thread.py:321)
2025-06-16 16:31:35,686 [INFO] scraper_thread - [SCRAPER] hatice.kurt833 | 1 (scraper_thread.py:321)
2025-06-16 16:31:41,174 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 3 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-06-16 16:31:43,780 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-06-16 16:31:43,780 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:224)
2025-06-16 16:31:43,780 [INFO] main - ✅ Scraper thread temizlendi (main.py:230)
2025-06-16 16:31:43,780 [INFO] main - 🔍 Son 1 dakikada toplam 3 kullanıcı: (main.py:247)
2025-06-16 16:31:43,780 [INFO] main -   - hatice.kurt833: Bekleniyor (2025-06-16 16:31:35) (main.py:249)
2025-06-16 16:31:43,780 [INFO] main -   - keniverse0: Bekleniyor (2025-06-16 16:31:30) (main.py:249)
2025-06-16 16:31:43,780 [INFO] main -   - theege61: Bekleniyor (2025-06-16 16:30:51) (main.py:249)
2025-06-16 16:31:43,780 [INFO] main - 2️⃣ Status Checker başlatılıyor (3 kullanıcı) (main.py:253)
2025-06-16 16:31:43,780 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:371)
2025-06-16 16:31:43,780 [INFO] main - 🔧 Phase lock alınıyor... (main.py:377)
2025-06-16 16:31:43,780 [INFO] main - 🔧 Phase lock alındı (main.py:379)
2025-06-16 16:31:43,780 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:387)
2025-06-16 16:31:43,780 [INFO] main - 2️⃣ Status Checker başlatılıyor (3 kullanıcı) (main.py:390)
2025-06-16 16:31:43,780 [INFO] main - 📋 Kullanıcılar: ['theege61', 'keniverse0', 'hatice.kurt833'] (main.py:391)
2025-06-16 16:31:43,780 [INFO] main - 📋 Publishers formatı: [{'username': 'theege61'}, {'username': 'keniverse0'}, {'username': 'hatice.kurt833'}] (main.py:395)
2025-06-16 16:31:43,780 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:397)
2025-06-16 16:31:43,780 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:407)
2025-06-16 16:31:43,780 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:408)
2025-06-16 16:31:43,780 [INFO] main - 🔧 Thread daemon: True (main.py:409)
2025-06-16 16:31:43,780 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:418)
2025-06-16 16:31:43,780 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-06-16 16:31:43,780 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:422)
2025-06-16 16:31:43,780 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 3 (status_checker.py:84)
2025-06-16 16:31:43,780 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:431)
2025-06-16 16:31:43,780 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 12960 (status_checker.py:84)
2025-06-16 16:31:43,780 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-06-16 16:31:43,780 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:31:43,780 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-06-16 16:31:43,780 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-06-16 16:31:46,967 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-06-16 16:31:46,967 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 3 kullanıcı işlenecek (status_checker.py:84)
2025-06-16 16:31:46,967 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'theege61'}, {'username': 'keniverse0'}, {'username': 'hatice.kurt833'}] (status_checker.py:84)
2025-06-16 16:31:46,967 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-06-16 16:31:46,967 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-06-16 16:31:46,967 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-06-16 16:31:46,967 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-06-16 16:31:46,967 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-06-16 16:31:46,967 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-06-16 16:31:46,967 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-06-16 16:31:46,967 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-06-16 16:31:48,935 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:31:51,952 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-06-16 16:31:51,952 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:31:51,952 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-06-16 16:31:51,952 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-06-16 16:31:54,476 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-06-16 16:31:54,550 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-06-16 16:31:56,286 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-06-16 16:31:57,142 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:84)
2025-06-16 16:31:57,146 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-06-16 16:31:57,148 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-06-16 16:32:00,579 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-06-16 16:32:00,579 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 3 kullanıcı). (status_checker.py:84)
2025-06-16 16:32:00,862 [INFO] main - 📊 Thread durumu: status_checker - Alive: True, Finished: False (main.py:126)
2025-06-16 16:32:03,767 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-06-16 16:32:07,787 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 3 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-06-16 16:32:10,735 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-06-16 16:32:12,325 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 3 (status_checker.py:84)
2025-06-16 16:32:12,329 [INFO] StatusChecker - [STATUS_CHECKER] 💾 3 kaydın durumu güncellendi. (status_checker.py:84)
2025-06-16 16:32:15,299 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-06-16 16:32:20,827 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-06-16 16:32:20,983 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-06-16 16:32:20,983 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-06-16 16:32:20,983 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:270)
2025-06-16 16:32:20,983 [INFO] main - ✅ Status Checker thread temizlendi (main.py:276)
2025-06-16 16:32:21,014 [INFO] main - 3️⃣ Message Sender başlatılıyor (1 kullanıcı) (main.py:288)
2025-06-16 16:32:21,014 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:452)
2025-06-16 16:32:21,014 [INFO] main - ✅ Thread temizlendi, scraper başlatılıyor (main.py:457)
2025-06-16 16:32:22,030 [INFO] main - 🔧 start_scraper çağrıldı (main.py:323)
2025-06-16 16:32:22,030 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:325)
2025-06-16 16:32:22,030 [INFO] main - 🔧 Phase lock alındı (main.py:328)
2025-06-16 16:32:22,030 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:345)
2025-06-16 16:32:22,030 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:348)
2025-06-16 16:32:22,030 [INFO] main - ✅ Scraper thread başlatıldı (main.py:362)
2025-06-16 16:32:26,389 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-06-16 16:32:26,389 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-06-16 16:32:33,109 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-06-16 16:32:38,477 [INFO] scraper_thread - [SCRAPER] theangarabebesi | 479 (scraper_thread.py:321)
2025-06-16 16:32:45,644 [INFO] scraper_thread - [SCRAPER] omerfaydin | 21 (scraper_thread.py:321)
2025-06-16 16:32:49,672 [INFO] scraper_thread - [SCRAPER] armenv4 | 12 (scraper_thread.py:321)
2025-06-16 16:32:53,972 [INFO] scraper_thread - [SCRAPER] saturnenesbs | 3 (scraper_thread.py:321)
2025-06-16 16:32:59,444 [INFO] scraper_thread - [SCRAPER] lewronebaba | 19 (scraper_thread.py:321)
2025-06-16 16:33:06,300 [INFO] scraper_thread - [SCRAPER] turgayselvinaz.ozkan | 479 (scraper_thread.py:321)
2025-06-16 16:33:10,910 [INFO] scraper_thread - [SCRAPER] bariton | 91 (scraper_thread.py:321)
2025-06-16 16:33:16,084 [INFO] scraper_thread - [SCRAPER] hurdaci01.34 | 150 (scraper_thread.py:321)
2025-06-16 16:33:21,837 [INFO] scraper_thread - [SCRAPER] _aleynaileumut | 15 (scraper_thread.py:321)
2025-06-16 16:33:26,111 [INFO] scraper_thread - [SCRAPER] ahmetagirmann | 359 (scraper_thread.py:321)
2025-06-16 16:33:31,611 [INFO] scraper_thread - [SCRAPER] minatopubgm1 | 1 (scraper_thread.py:321)
2025-06-16 16:33:37,372 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 11 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-06-16 16:33:40,061 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-06-16 16:33:40,061 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:224)
2025-06-16 16:33:40,061 [INFO] main - ✅ Scraper thread temizlendi (main.py:230)
2025-06-16 16:33:40,061 [INFO] main - 🔍 Son 1 dakikada toplam 10 kullanıcı: (main.py:247)
2025-06-16 16:33:40,061 [INFO] main -   - minatopubgm1: Bekleniyor (2025-06-16 16:33:31) (main.py:249)
2025-06-16 16:33:40,061 [INFO] main -   - ahmetagirmann: Bekleniyor (2025-06-16 16:33:26) (main.py:249)
2025-06-16 16:33:40,061 [INFO] main -   - _aleynaileumut: Bekleniyor (2025-06-16 16:33:21) (main.py:249)
2025-06-16 16:33:40,061 [INFO] main -   - hurdaci01.34: Bekleniyor (2025-06-16 16:33:16) (main.py:249)
2025-06-16 16:33:40,061 [INFO] main -   - bariton: Bekleniyor (2025-06-16 16:33:10) (main.py:249)
2025-06-16 16:33:40,061 [INFO] main -   - turgayselvinaz.ozkan: Bekleniyor (2025-06-16 16:33:06) (main.py:249)
2025-06-16 16:33:40,061 [INFO] main -   - lewronebaba: Bekleniyor (2025-06-16 16:32:59) (main.py:249)
2025-06-16 16:33:40,061 [INFO] main -   - saturnenesbs: Bekleniyor (2025-06-16 16:32:53) (main.py:249)
2025-06-16 16:33:40,061 [INFO] main -   - armenv4: Bekleniyor (2025-06-16 16:32:49) (main.py:249)
2025-06-16 16:33:40,061 [INFO] main -   - omerfaydin: Bekleniyor (2025-06-16 16:32:45) (main.py:249)
2025-06-16 16:33:40,061 [INFO] main - 2️⃣ Status Checker başlatılıyor (10 kullanıcı) (main.py:253)
2025-06-16 16:33:40,061 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:371)
2025-06-16 16:33:40,061 [INFO] main - 🔧 Phase lock alınıyor... (main.py:377)
2025-06-16 16:33:40,061 [INFO] main - 🔧 Phase lock alındı (main.py:379)
2025-06-16 16:33:40,061 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:387)
2025-06-16 16:33:40,061 [INFO] main - 2️⃣ Status Checker başlatılıyor (10 kullanıcı) (main.py:390)
2025-06-16 16:33:40,061 [INFO] main - 📋 Kullanıcılar: ['omerfaydin', 'armenv4', 'saturnenesbs', 'lewronebaba', 'turgayselvinaz.ozkan', 'bariton', 'hurdaci01.34', '_aleynaileumut', 'ahmetagirmann', 'minatopubgm1'] (main.py:391)
2025-06-16 16:33:40,061 [INFO] main - 📋 Publishers formatı: [{'username': 'omerfaydin'}, {'username': 'armenv4'}, {'username': 'saturnenesbs'}, {'username': 'lewronebaba'}, {'username': 'turgayselvinaz.ozkan'}, {'username': 'bariton'}, {'username': 'hurdaci01.34'}, {'username': '_aleynaileumut'}, {'username': 'ahmetagirmann'}, {'username': 'minatopubgm1'}] (main.py:395)
2025-06-16 16:33:40,061 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:397)
2025-06-16 16:33:40,061 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:407)
2025-06-16 16:33:40,061 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:408)
2025-06-16 16:33:40,061 [INFO] main - 🔧 Thread daemon: True (main.py:409)
2025-06-16 16:33:40,061 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:418)
2025-06-16 16:33:40,061 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-06-16 16:33:40,061 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:422)
2025-06-16 16:33:40,077 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 10 (status_checker.py:84)
2025-06-16 16:33:40,077 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:431)
2025-06-16 16:33:40,077 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 6792 (status_checker.py:84)
2025-06-16 16:33:40,077 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-06-16 16:33:40,077 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:33:40,077 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-06-16 16:33:40,077 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-06-16 16:33:43,248 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-06-16 16:33:43,248 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 10 kullanıcı işlenecek (status_checker.py:84)
2025-06-16 16:33:43,248 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'omerfaydin'}, {'username': 'armenv4'}, {'username': 'saturnenesbs'}] (status_checker.py:84)
2025-06-16 16:33:43,248 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-06-16 16:33:43,248 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-06-16 16:33:43,248 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-06-16 16:33:43,248 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-06-16 16:33:43,248 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-06-16 16:33:43,248 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-06-16 16:33:43,248 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-06-16 16:33:43,248 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-06-16 16:33:45,097 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:33:48,108 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-06-16 16:33:48,108 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:33:48,108 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-06-16 16:33:48,108 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-06-16 16:33:55,702 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-06-16 16:33:55,709 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-06-16 16:33:55,745 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-06-16 16:33:55,749 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:84)
2025-06-16 16:33:55,749 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-06-16 16:33:55,757 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-06-16 16:33:58,875 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-06-16 16:33:58,876 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 10 kullanıcı). (status_checker.py:84)
2025-06-16 16:34:01,800 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-06-16 16:34:07,038 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 10 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-06-16 16:34:10,347 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-06-16 16:34:11,680 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 10 (status_checker.py:84)
2025-06-16 16:34:11,685 [INFO] StatusChecker - [STATUS_CHECKER] 💾 10 kaydın durumu güncellendi. (status_checker.py:84)
2025-06-16 16:34:15,001 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-06-16 16:34:19,467 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-06-16 16:34:19,608 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-06-16 16:34:19,608 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-06-16 16:34:19,623 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:270)
2025-06-16 16:34:19,623 [INFO] main - ✅ Status Checker thread temizlendi (main.py:276)
2025-06-16 16:34:19,655 [INFO] main - 3️⃣ Message Sender başlatılıyor (4 kullanıcı) (main.py:288)
2025-06-16 16:34:19,655 [INFO] main - ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor (main.py:452)
2025-06-16 16:34:19,655 [INFO] main - ✅ Thread temizlendi, scraper başlatılıyor (main.py:457)
2025-06-16 16:34:20,670 [INFO] main - 🔧 start_scraper çağrıldı (main.py:323)
2025-06-16 16:34:20,670 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:325)
2025-06-16 16:34:20,671 [INFO] main - 🔧 Phase lock alındı (main.py:328)
2025-06-16 16:34:20,671 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:345)
2025-06-16 16:34:20,671 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:348)
2025-06-16 16:34:20,671 [INFO] main - ✅ Scraper thread başlatıldı (main.py:362)
2025-06-16 16:34:25,019 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-06-16 16:34:25,019 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-06-16 16:34:31,827 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-06-16 16:34:36,139 [INFO] scraper_thread - [SCRAPER] theege61 | 103 (scraper_thread.py:321)
2025-06-16 16:34:48,794 [INFO] scraper_thread - [SCRAPER] tt.bal.pubg | 101 (scraper_thread.py:321)
2025-06-16 16:35:00,001 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-06-16 16:35:06,489 [INFO] __main__ - 📨 Komut: stop (ID: 383) (automation_worker.py:214)
2025-06-16 16:35:08,144 [INFO] __main__ - ⏹️ Otomasyon durduruluyor... (automation_worker.py:158)
2025-06-16 16:35:08,144 [INFO] main - ⏹️ Otomasyon durduruluyor... (main.py:206)
2025-06-16 16:35:08,146 [INFO] main - ✅ Otomasyon durduruldu (main.py:215)
2025-06-16 16:35:08,147 [INFO] __main__ - 🧹 Otomasyon temizleniyor... (automation_worker.py:92)
2025-06-16 16:35:08,148 [INFO] __main__ - ✅ Otomasyon temizlendi (automation_worker.py:97)
2025-06-16 16:35:08,149 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 16:35:08,530 [ERROR] scraper_thread - [SCRAPER] Scraper döngüsünde hata: ('Connection aborted.', ConnectionResetError(10054, 'Varolan bir bağlantı uzaktaki bir ana bilgisayar tarafından zorla kapatıldı', None, 10054, None)) (scraper_thread.py:325)
2025-06-16 16:35:08,530 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 2 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-06-16 16:35:10,155 [INFO] main - 🔍 Monitör durduruldu (main.py:197)
2025-06-16 16:35:11,452 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 16:35:12,592 [WARNING] urllib3.connectionpool - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001D6E5BCA6B0>: Failed to establish a new connection: [WinError 10061] Hedef makine etkin olarak reddettiğinden bağlantı kurulamadı')': /session/731e6ce01db4bf1c4614ab71b1e064cb (connectionpool.py:827)
2025-06-16 16:35:12,998 [INFO] __main__ - ✅ Otomasyon durduruldu (automation_worker.py:163)
2025-06-16 16:35:17,045 [WARNING] urllib3.connectionpool - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001D6E5C70430>: Failed to establish a new connection: [WinError 10061] Hedef makine etkin olarak reddettiğinden bağlantı kurulamadı')': /session/731e6ce01db4bf1c4614ab71b1e064cb (connectionpool.py:827)
2025-06-16 16:35:21,077 [WARNING] urllib3.connectionpool - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001D6E5BCAFE0>: Failed to establish a new connection: [WinError 10061] Hedef makine etkin olarak reddettiğinden bağlantı kurulamadı')': /session/731e6ce01db4bf1c4614ab71b1e064cb (connectionpool.py:827)
2025-06-16 16:35:29,217 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-06-16 16:35:31,092 [INFO] __main__ - 📨 Komut: start (ID: 384) (automation_worker.py:214)
2025-06-16 16:35:31,092 [INFO] __main__ - 🔄 Tüm Chrome işlemleri kapatılıyor... (automation_worker.py:69)
2025-06-16 16:35:34,264 [INFO] __main__ - ✅ Tüm Chrome işlemleri kapatıldı (automation_worker.py:82)
2025-06-16 16:35:34,264 [INFO] __main__ - 🚀 TikTok Otomasyon Sistemi başlatılıyor... (automation_worker.py:127)
2025-06-16 16:35:34,265 [INFO] __main__ - ⏱️ Döngü süresi: 1 dakika (automation_worker.py:128)
2025-06-16 16:35:34,265 [INFO] __main__ - ✅ Veritabanına bağlandı (automation_worker.py:134)
2025-06-16 16:35:34,265 [INFO] main - 🚀 Otomasyon döngüsü başlatılıyor... (main.py:86)
2025-06-16 16:35:34,265 [INFO] main - 🔍 Monitör başlatıldı (main.py:105)
2025-06-16 16:35:34,265 [INFO] main - 🔧 start_scraper çağrıldı (main.py:323)
2025-06-16 16:35:34,265 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:325)
2025-06-16 16:35:34,265 [INFO] main - 🔧 Phase lock alındı (main.py:328)
2025-06-16 16:35:34,265 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:345)
2025-06-16 16:35:34,265 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:348)
2025-06-16 16:35:34,265 [INFO] main - ✅ Scraper thread başlatıldı (main.py:362)
2025-06-16 16:35:34,265 [INFO] __main__ - ✅ Otomasyon başlatıldı (ID: 384) (automation_worker.py:150)
2025-06-16 16:35:38,639 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-06-16 16:35:38,655 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-06-16 16:35:46,256 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-06-16 16:36:00,508 [INFO] main - 📊 Thread durumu: scraper - Alive: True, Finished: False (main.py:126)
2025-06-16 16:36:40,425 [INFO] scraper_thread - [SCRAPER] theege61 | 80 (scraper_thread.py:321)
2025-06-16 16:36:47,723 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 1 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-06-16 16:36:50,311 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-06-16 16:36:50,311 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:224)
2025-06-16 16:36:50,311 [INFO] main - ✅ Scraper thread temizlendi (main.py:230)
2025-06-16 16:36:50,311 [INFO] main - 🔍 Son 1 dakikada toplam 1 kullanıcı: (main.py:247)
2025-06-16 16:36:50,311 [INFO] main -   - theege61: Bekleniyor (2025-06-16 16:36:40) (main.py:249)
2025-06-16 16:36:50,311 [INFO] main - 2️⃣ Status Checker başlatılıyor (1 kullanıcı) (main.py:253)
2025-06-16 16:36:50,311 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:371)
2025-06-16 16:36:50,311 [INFO] main - 🔧 Phase lock alınıyor... (main.py:377)
2025-06-16 16:36:50,311 [INFO] main - 🔧 Phase lock alındı (main.py:379)
2025-06-16 16:36:50,311 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:387)
2025-06-16 16:36:50,311 [INFO] main - 2️⃣ Status Checker başlatılıyor (1 kullanıcı) (main.py:390)
2025-06-16 16:36:50,311 [INFO] main - 📋 Kullanıcılar: ['theege61'] (main.py:391)
2025-06-16 16:36:50,311 [INFO] main - 📋 Publishers formatı: [{'username': 'theege61'}] (main.py:395)
2025-06-16 16:36:50,311 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:397)
2025-06-16 16:36:50,311 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:407)
2025-06-16 16:36:50,311 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:408)
2025-06-16 16:36:50,311 [INFO] main - 🔧 Thread daemon: True (main.py:409)
2025-06-16 16:36:50,311 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:418)
2025-06-16 16:36:50,311 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-06-16 16:36:50,311 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:422)
2025-06-16 16:36:50,311 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 1 (status_checker.py:84)
2025-06-16 16:36:50,311 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:431)
2025-06-16 16:36:50,311 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 6328 (status_checker.py:84)
2025-06-16 16:36:50,311 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-06-16 16:36:50,311 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:36:50,311 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-06-16 16:36:50,311 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-06-16 16:36:53,498 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-06-16 16:36:53,498 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 1 kullanıcı işlenecek (status_checker.py:84)
2025-06-16 16:36:53,498 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'theege61'}] (status_checker.py:84)
2025-06-16 16:36:53,498 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-06-16 16:36:53,498 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-06-16 16:36:53,498 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-06-16 16:36:53,498 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-06-16 16:36:53,498 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-06-16 16:36:53,498 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-06-16 16:36:53,498 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-06-16 16:36:53,498 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-06-16 16:36:59,434 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:37:00,717 [INFO] main - 📊 Thread durumu: status_checker - Alive: True, Finished: False (main.py:126)
2025-06-16 16:37:02,593 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-06-16 16:37:02,593 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:37:02,593 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-06-16 16:37:02,593 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-06-16 16:37:03,134 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-06-16 16:37:03,134 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-06-16 16:37:08,677 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-06-16 16:37:09,485 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:84)
2025-06-16 16:37:09,485 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-06-16 16:37:09,487 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-06-16 16:37:12,510 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-06-16 16:37:12,568 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 1 kullanıcı). (status_checker.py:84)
2025-06-16 16:37:16,127 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-06-16 16:37:20,363 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 1 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-06-16 16:37:23,155 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-06-16 16:37:24,451 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 1 (status_checker.py:84)
2025-06-16 16:37:27,136 [INFO] StatusChecker - [STATUS_CHECKER] 💾 1 kaydın durumu güncellendi. (status_checker.py:84)
2025-06-16 16:37:30,547 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-06-16 16:37:30,893 [INFO] main - 📊 Thread durumu: status_checker - Alive: True, Finished: False (main.py:126)
2025-06-16 16:37:35,155 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-06-16 16:37:35,311 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-06-16 16:37:35,311 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-06-16 16:37:35,311 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:270)
2025-06-16 16:37:35,311 [INFO] main - ✅ Status Checker thread temizlendi (main.py:276)
2025-06-16 16:37:35,342 [INFO] main - 🔄 Uygun kullanıcı yok, scraper tekrar başlatılıyor (main.py:291)
2025-06-16 16:37:37,358 [INFO] main - 🔧 start_scraper çağrıldı (main.py:323)
2025-06-16 16:37:37,358 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:325)
2025-06-16 16:37:37,358 [INFO] main - 🔧 Phase lock alındı (main.py:328)
2025-06-16 16:37:37,358 [INFO] main - 🔧 Current thread None, scraper başlatılıyor (main.py:345)
2025-06-16 16:37:37,358 [INFO] main - 1️⃣ Scraper başlatılıyor (süre: 1 dk) (main.py:348)
2025-06-16 16:37:37,358 [INFO] main - ✅ Scraper thread başlatıldı (main.py:362)
2025-06-16 16:37:41,619 [INFO] scraper_thread - [SCRAPER] ✅ Bot algılama önlemleri uygulandı (scraper_thread.py:321)
2025-06-16 16:37:41,619 [INFO] scraper_thread - [SCRAPER] TikTok Live sayfasına gidiliyor... (scraper_thread.py:321)
2025-06-16 16:37:49,452 [INFO] scraper_thread - [SCRAPER] Scraper başladı, süre: 1.0 dk (scraper_thread.py:321)
2025-06-16 16:37:55,433 [INFO] scraper_thread - [SCRAPER] theege61 | 93 (scraper_thread.py:321)
2025-06-16 16:38:00,543 [INFO] scraper_thread - [SCRAPER] malipmtt | 5 (scraper_thread.py:321)
2025-06-16 16:38:05,039 [INFO] scraper_thread - [SCRAPER] gaminghub.47 | 34 (scraper_thread.py:321)
2025-06-16 16:38:11,929 [INFO] scraper_thread - [SCRAPER] gulumser_konya_42 | 70 (scraper_thread.py:321)
2025-06-16 16:38:16,944 [INFO] scraper_thread - [SCRAPER] psikolojikdeli06 | 4 (scraper_thread.py:321)
2025-06-16 16:38:20,828 [INFO] scraper_thread - [SCRAPER] sametss92 | 1 (scraper_thread.py:321)
2025-06-16 16:38:24,963 [INFO] scraper_thread - [SCRAPER] mehmetalierbilofficial | 1200 (scraper_thread.py:321)
2025-06-16 16:38:30,201 [INFO] scraper_thread - [SCRAPER] hurdaci01.34 | 121 (scraper_thread.py:321)
2025-06-16 16:38:35,018 [INFO] scraper_thread - [SCRAPER] bariton | 71 (scraper_thread.py:321)
2025-06-16 16:38:39,890 [INFO] scraper_thread - [SCRAPER] azi28.07 | 1 (scraper_thread.py:321)
2025-06-16 16:38:44,271 [INFO] scraper_thread - [SCRAPER] _aleynaileumut | 17 (scraper_thread.py:321)
2025-06-16 16:38:49,255 [INFO] scraper_thread - [SCRAPER] muratcelikcelik | 3 (scraper_thread.py:321)
2025-06-16 16:38:53,216 [INFO] scraper_thread - [SCRAPER] ✅ Toplam 12 benzersiz kullanıcı bulundu (scraper_thread.py:321)
2025-06-16 16:38:55,842 [INFO] scraper_thread - [SCRAPER] ✅ Chrome kapatıldı (scraper_thread.py:321)
2025-06-16 16:38:55,842 [INFO] main - 🔄 Scraper callback çağrıldı (main.py:224)
2025-06-16 16:38:55,842 [INFO] main - ✅ Scraper thread temizlendi (main.py:230)
2025-06-16 16:38:55,842 [INFO] main - 🔍 Son 1 dakikada toplam 12 kullanıcı: (main.py:247)
2025-06-16 16:38:55,842 [INFO] main -   - muratcelikcelik: Bekleniyor (2025-06-16 16:38:49) (main.py:249)
2025-06-16 16:38:55,842 [INFO] main -   - _aleynaileumut: Bekleniyor (2025-06-16 16:38:44) (main.py:249)
2025-06-16 16:38:55,842 [INFO] main -   - azi28.07: Bekleniyor (2025-06-16 16:38:39) (main.py:249)
2025-06-16 16:38:55,842 [INFO] main -   - bariton: Bekleniyor (2025-06-16 16:38:35) (main.py:249)
2025-06-16 16:38:55,842 [INFO] main -   - hurdaci01.34: Bekleniyor (2025-06-16 16:38:30) (main.py:249)
2025-06-16 16:38:55,842 [INFO] main -   - mehmetalierbilofficial: Bekleniyor (2025-06-16 16:38:24) (main.py:249)
2025-06-16 16:38:55,842 [INFO] main -   - sametss92: Bekleniyor (2025-06-16 16:38:20) (main.py:249)
2025-06-16 16:38:55,842 [INFO] main -   - psikolojikdeli06: Bekleniyor (2025-06-16 16:38:16) (main.py:249)
2025-06-16 16:38:55,842 [INFO] main -   - gulumser_konya_42: Bekleniyor (2025-06-16 16:38:11) (main.py:249)
2025-06-16 16:38:55,842 [INFO] main -   - gaminghub.47: Bekleniyor (2025-06-16 16:38:05) (main.py:249)
2025-06-16 16:38:55,842 [INFO] main -   - malipmtt: Bekleniyor (2025-06-16 16:38:00) (main.py:249)
2025-06-16 16:38:55,842 [INFO] main -   - theege61: Bekleniyor (2025-06-16 16:37:55) (main.py:249)
2025-06-16 16:38:55,842 [INFO] main - 2️⃣ Status Checker başlatılıyor (12 kullanıcı) (main.py:253)
2025-06-16 16:38:55,842 [INFO] main - 🔧 start_status_checker çağrıldı (main.py:371)
2025-06-16 16:38:55,842 [INFO] main - 🔧 Phase lock alınıyor... (main.py:377)
2025-06-16 16:38:55,842 [INFO] main - 🔧 Phase lock alındı (main.py:379)
2025-06-16 16:38:55,842 [INFO] main - 🔧 Current thread None, devam ediliyor (main.py:387)
2025-06-16 16:38:55,842 [INFO] main - 2️⃣ Status Checker başlatılıyor (12 kullanıcı) (main.py:390)
2025-06-16 16:38:55,842 [INFO] main - 📋 Kullanıcılar: ['theege61', 'malipmtt', 'gaminghub.47', 'gulumser_konya_42', 'psikolojikdeli06', 'sametss92', 'mehmetalierbilofficial', 'hurdaci01.34', 'bariton', 'azi28.07', '_aleynaileumut', 'muratcelikcelik'] (main.py:391)
2025-06-16 16:38:55,842 [INFO] main - 📋 Publishers formatı: [{'username': 'theege61'}, {'username': 'malipmtt'}, {'username': 'gaminghub.47'}, {'username': 'gulumser_konya_42'}, {'username': 'psikolojikdeli06'}, {'username': 'sametss92'}, {'username': 'mehmetalierbilofficial'}, {'username': 'hurdaci01.34'}, {'username': 'bariton'}, {'username': 'azi28.07'}, {'username': '_aleynaileumut'}, {'username': 'muratcelikcelik'}] (main.py:395)
2025-06-16 16:38:55,842 [INFO] main - 🔧 StatusCheckerThread oluşturuluyor... (main.py:397)
2025-06-16 16:38:55,842 [INFO] main - ✅ StatusCheckerThread oluşturuldu (main.py:407)
2025-06-16 16:38:55,842 [INFO] main - 🔧 Thread tipi: <class 'status_checker.StatusCheckerThread'> (main.py:408)
2025-06-16 16:38:55,842 [INFO] main - 🔧 Thread daemon: True (main.py:409)
2025-06-16 16:38:55,842 [INFO] main - 🚀 Status Checker thread başlatılıyor... (main.py:418)
2025-06-16 16:38:55,842 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 StatusChecker thread RUN metodu başladı (status_checker.py:84)
2025-06-16 16:38:55,842 [INFO] main - ✅ Status Checker thread.start() çağrıldı (main.py:422)
2025-06-16 16:38:55,842 [INFO] StatusChecker - [STATUS_CHECKER] 📊 İşlenecek kullanıcı sayısı: 12 (status_checker.py:84)
2025-06-16 16:38:55,842 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Thread ID: 13372 (status_checker.py:84)
2025-06-16 16:38:55,842 [INFO] main - ✅ Status Checker thread başlatıldı, callback bekliyor (main.py:431)
2025-06-16 16:38:55,858 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 _running durumu: True (status_checker.py:84)
2025-06-16 16:38:55,858 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker thread başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:38:55,858 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Ana işlem bloğuna girdi (status_checker.py:84)
2025-06-16 16:38:55,858 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome temizleniyor... (status_checker.py:84)
2025-06-16 16:38:59,030 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome temizlendi (status_checker.py:84)
2025-06-16 16:38:59,030 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 12 kullanıcı işlenecek (status_checker.py:84)
2025-06-16 16:38:59,030 [INFO] StatusChecker - [STATUS_CHECKER] 📋 İlk 3 kullanıcı: [{'username': 'theege61'}, {'username': 'malipmtt'}, {'username': 'gaminghub.47'}] (status_checker.py:84)
2025-06-16 16:38:59,030 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver başlatılıyor... (status_checker.py:84)
2025-06-16 16:38:59,030 [INFO] StatusChecker - [STATUS_CHECKER] 🚀 Chrome WebDriver kurulumu başlıyor... (status_checker.py:84)
2025-06-16 16:38:59,030 [INFO] StatusChecker - [STATUS_CHECKER] 📁 ChromeDriver yolu: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (status_checker.py:84)
2025-06-16 16:38:59,030 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:84)
2025-06-16 16:38:59,030 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome profil klasörü: Profile 1 (status_checker.py:84)
2025-06-16 16:38:59,030 [INFO] StatusChecker - [STATUS_CHECKER] 📁 Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (status_checker.py:84)
2025-06-16 16:38:59,030 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Tüm yollar doğrulandı (status_checker.py:84)
2025-06-16 16:38:59,030 [INFO] StatusChecker - [STATUS_CHECKER] 🔧 Chrome servisi başlatılıyor... (status_checker.py:84)
2025-06-16 16:39:00,952 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:39:03,967 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Gelişmiş bot algılama önlemleri uygulandı (status_checker.py:84)
2025-06-16 16:39:03,967 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Chrome WebDriver başarıyla başlatıldı (status_checker.py:84)
2025-06-16 16:39:03,967 [INFO] StatusChecker - [STATUS_CHECKER] 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:84)
2025-06-16 16:39:03,967 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:84)
2025-06-16 16:39:09,898 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yükleme komutu gönderildi (status_checker.py:84)
2025-06-16 16:39:09,915 [INFO] StatusChecker - [STATUS_CHECKER] ⏳ Sayfa yüklenmesi bekleniyor (10 saniye)... (status_checker.py:84)
2025-06-16 16:39:10,323 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Sayfa yüklendi (ready_state: complete) (status_checker.py:84)
2025-06-16 16:39:10,950 [INFO] StatusChecker - [STATUS_CHECKER] 📍 Mevcut URL: https://live-backstage.tiktok.com/portal/overview (status_checker.py:84)
2025-06-16 16:39:10,950 [INFO] StatusChecker - [STATUS_CHECKER] ✅ TikTok sayfasına erişim sağlandı (status_checker.py:84)
2025-06-16 16:39:10,965 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Pop-up'lar kontrol ediliyor... (status_checker.py:84)
2025-06-16 16:39:14,235 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Pop-up kontrolü tamamlandı (status_checker.py:84)
2025-06-16 16:39:14,235 [INFO] StatusChecker - [STATUS_CHECKER] 📦 Chunk 1/1 (içinde 12 kullanıcı). (status_checker.py:84)
2025-06-16 16:39:18,003 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Panel açıldı (davet butonu). (status_checker.py:84)
2025-06-16 16:39:23,612 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 12 kullanıcı textarea'ya yazıldı. (status_checker.py:84)
2025-06-16 16:39:26,106 [INFO] StatusChecker - [STATUS_CHECKER] ✅ 'İleri' butonuna tıklandı. (status_checker.py:84)
2025-06-16 16:39:28,531 [INFO] StatusChecker - [STATUS_CHECKER] 🔍 Toplam kullanıcı sayısı: 12 (status_checker.py:84)
2025-06-16 16:39:28,542 [INFO] StatusChecker - [STATUS_CHECKER] 💾 12 kaydın durumu güncellendi. (status_checker.py:84)
2025-06-16 16:39:31,954 [INFO] StatusChecker - [STATUS_CHECKER] ✅ Geri butonuna basılarak panel kapatıldı. (status_checker.py:84)
2025-06-16 16:39:36,420 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Chrome WebDriver kapatıldı (status_checker.py:84)
2025-06-16 16:39:53,452 [INFO] StatusChecker - [STATUS_CHECKER] 🔄 Tüm Chrome işlemleri kapatıldı (status_checker.py:84)
2025-06-16 16:39:53,452 [INFO] StatusChecker - [STATUS_CHECKER] ✅ StatusChecker tamamlandı (status_checker.py:84)
2025-06-16 16:39:53,452 [INFO] main - 🔄 Status Checker callback çağrıldı (main.py:270)
2025-06-16 16:39:53,452 [INFO] main - ✅ Status Checker thread temizlendi (main.py:276)
2025-06-16 16:39:53,498 [INFO] main - 3️⃣ Message Sender başlatılıyor (5 kullanıcı) (main.py:288)
2025-06-16 16:39:53,498 [INFO] main - 3️⃣ Message Sender başlatılıyor (5 kullanıcı) (main.py:470)
2025-06-16 16:39:53,498 [INFO] MessageSender - [MESSAGE_SENDER] 📩 MessageSender başlatıldı. (message_sender_thread.py:443)
2025-06-16 16:39:55,733 [INFO] MessageSender - [MESSAGE_SENDER] ✅ Chrome işlemleri kapatıldı. (message_sender_thread.py:443)
2025-06-16 16:39:55,734 [INFO] MessageSender - [MESSAGE_SENDER] ✅ Chrome yolu doğrulandı (message_sender_thread.py:443)
2025-06-16 16:39:55,734 [INFO] MessageSender - [MESSAGE_SENDER] ✅ Chrome binary yolu ayarlandı: C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe (message_sender_thread.py:443)
2025-06-16 16:39:55,734 [INFO] MessageSender - [MESSAGE_SENDER] ✅ Chrome profil yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data, Profil: Profile 1 (message_sender_thread.py:443)
2025-06-16 16:39:55,734 [INFO] MessageSender - [MESSAGE_SENDER] ✅ ChromeDriver başlatılıyor: C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe (message_sender_thread.py:443)
2025-06-16 16:39:57,908 [INFO] MessageSender - [MESSAGE_SENDER] ✅ Bot algılama önlemleri uygulandı (message_sender_thread.py:443)
2025-06-16 16:40:00,014 [INFO] main - 📊 Thread durumu: message_sender - Alive: True, Finished: False (main.py:126)
2025-06-16 16:40:24,610 [INFO] MessageSender - [MESSAGE_SENDER] 🔍 İşleniyor: malipmtt (message_sender_thread.py:443)
2025-06-16 16:40:30,865 [INFO] main - 📊 Thread durumu: message_sender - Alive: True, Finished: False (main.py:126)
2025-06-16 16:41:00,995 [INFO] main - 📊 Thread durumu: message_sender - Alive: True, Finished: False (main.py:126)
2025-06-16 16:41:40,713 [INFO] MessageSender - [MESSAGE_SENDER] Veritabanı güncellemesi: ID 260857 için 'Mesaj Gönderildi' olarak güncellendi. (message_sender_thread.py:443)
2025-06-16 16:41:40,713 [INFO] MessageSender - [MESSAGE_SENDER] [malipmtt] adlı kullanıcıya mesaj gönderildi. (message_sender_thread.py:443)
2025-06-16 16:41:40,715 [INFO] MessageSender - [MESSAGE_SENDER] 🔍 İşleniyor: gaminghub.47 (message_sender_thread.py:443)
2025-06-16 16:43:00,111 [INFO] MessageSender - [MESSAGE_SENDER] Veritabanı güncellemesi: ID 260858 için 'Mesaj Gönderildi' olarak güncellendi. (message_sender_thread.py:443)
2025-06-16 16:43:00,118 [INFO] MessageSender - [MESSAGE_SENDER] [gaminghub.47] adlı kullanıcıya mesaj gönderildi. (message_sender_thread.py:443)
2025-06-16 16:43:00,119 [INFO] MessageSender - [MESSAGE_SENDER] 🔍 İşleniyor: sametss92 (message_sender_thread.py:443)
2025-06-16 16:43:00,812 [INFO] main - 📊 Thread durumu: message_sender - Alive: True, Finished: False (main.py:126)
2025-06-16 16:44:22,318 [INFO] MessageSender - [MESSAGE_SENDER] Veritabanı güncellemesi: ID 260861 için 'Mesaj Gönderildi' olarak güncellendi. (message_sender_thread.py:443)
2025-06-16 16:44:22,318 [INFO] MessageSender - [MESSAGE_SENDER] [sametss92] adlı kullanıcıya mesaj gönderildi. (message_sender_thread.py:443)
2025-06-16 16:44:22,320 [INFO] MessageSender - [MESSAGE_SENDER] 🔍 İşleniyor: azi28.07 (message_sender_thread.py:443)
2025-06-16 16:44:30,220 [INFO] main - 📊 Thread durumu: message_sender - Alive: True, Finished: False (main.py:126)
2025-06-16 16:45:00,334 [INFO] main - 📊 Thread durumu: message_sender - Alive: True, Finished: False (main.py:126)
2025-06-16 16:45:38,559 [INFO] MessageSender - [MESSAGE_SENDER] Veritabanı güncellemesi: ID 260865 için 'Mesaj Gönderildi' olarak güncellendi. (message_sender_thread.py:443)
2025-06-16 16:45:51,951 [INFO] MessageSender - [MESSAGE_SENDER] [azi28.07] adlı kullanıcıya mesaj gönderildi. (message_sender_thread.py:443)
2025-06-16 16:45:52,006 [INFO] MessageSender - [MESSAGE_SENDER] 🔍 İşleniyor: muratcelikcelik (message_sender_thread.py:443)
2025-06-16 16:45:53,822 [WARNING] main - ⚠️ message_sender thread 600 saniyedir yanıt vermiyor, yeniden başlatılıyor... (main.py:167)
2025-06-16 16:45:53,841 [INFO] MessageSender - [MESSAGE_SENDER] ⏹ MessageSender durdurma sinyali alındı. (message_sender_thread.py:443)
2025-06-16 16:45:53,894 [INFO] main - 🔄 Timeout nedeniyle scraper yeniden başlatılıyor (main.py:184)
2025-06-16 16:45:59,969 [INFO] main - 🔧 start_scraper çağrıldı (main.py:323)
2025-06-16 16:46:00,475 [INFO] main - 🔧 Phase lock alınmaya çalışılıyor... (main.py:325)
2025-06-16 16:46:05,898 [INFO] MessageSender - [MESSAGE_SENDER] 📩 Tüm mesajlar başarıyla gönderildi, yeni döngü başlatılıyor... (message_sender_thread.py:443)
2025-06-16 16:46:09,608 [INFO] MessageSender - [MESSAGE_SENDER] 🔄 Chrome driver kapatıldı. (message_sender_thread.py:443)
2025-06-16 16:46:09,983 [INFO] MessageSender - [MESSAGE_SENDER] 🔄 Tüm Chrome işlemleri kapatıldı (message_sender_thread.py:443)
2025-06-16 16:46:09,983 [INFO] MessageSender - [MESSAGE_SENDER] ✅ MessageSender tamamlandı (message_sender_thread.py:443)
2025-06-16 16:46:09,983 [INFO] main - 🔄 Message Sender callback çağrıldı (main.py:305)
2025-06-16 16:58:30,442 [INFO] __main__ - 🚀 TikTok Otomasyon Worker başlatılıyor... (automation_worker.py:252)
2025-06-16 16:58:30,443 [INFO] __main__ - 📁 Dizin: C:\Users\<USER>\Desktop\Tuber X-Akademi\Yayıncı Avı Modüler Yeni (automation_worker.py:253)
2025-06-16 16:58:30,453 [INFO] __main__ - ✅ Veritabanı bağlantısı başarılı (automation_worker.py:259)
2025-06-16 16:58:30,466 [INFO] __main__ - ✅ params alanı LONGTEXT'e güncellendi (automation_worker.py:502) (automation_worker.py:185)
2025-06-16 16:58:30,467 [INFO] __main__ - ✅ Sistem hazır - komutlar bekleniyor (automation_worker.py:198)
