import os
import subprocess
import random
import time
import logging
import threading
from typing import List, Dict, Optional
from dataclasses import dataclass
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import (
    TimeoutException,
    WebDriverException,
    ElementClickInterceptedException
)
from constants import config_manager
from utils import random_sleep

logger = logging.getLogger('MessageSender')

@dataclass
class User:
    id: int
    username: str
    status: str
    message_log: Optional[str] = None
    timestamp: Optional[datetime] = None
    viewer_count: Optional[int] = None
    link: Optional[str] = None
    sorgu_tarihi: Optional[datetime] = None

class MessageSenderThread(threading.Thread):
    """
    <PERSON>j gönderme işlemlerini arka planda gerçekleştiren normal Python Thread.
    Stop sinyali geldiğinde _is_running bayrağı False yapılır.
    """

    def __init__(self, db_manager, users: Optional[List[Dict]] = None, parent=None, callback=None) -> None:
        super().__init__(daemon=True)
        self.db_manager = db_manager
        # STANDART Chrome yolları - main.py ile TAMAMEN aynı yolları kullan
        self.driver_path: str = r"C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe"
        self.chrome_profile_path: str = r"C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data"
        self.chrome_binary_path: str = r"C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe"
        self._is_running: bool = False
        self.users: List[User] = [User(**user) for user in (users or [])]
        self.logger = logger
        self.retry_count: int = 3
        self.retry_delay: int = 5
        self.cycle_start_time: Optional[datetime] = None  # Döngü başlangıç zamanı
        self.is_finished: bool = False  # Thread tamamlanma flag'i
        self.callback = callback  # Tamamlanma callback'i
        
    def run(self) -> None:
        self._is_running = True
        self.log("📩 MessageSender başlatıldı.")
        driver: Optional[webdriver.Chrome] = None

        try:
            driver = self.setup_driver()
            if not self._is_running:
                return

            self.navigate_to_messages(driver)
            if not self._is_running:
                return

            if not self.users:
                self.log("⚠ Gönderilecek kullanıcı yok.")
                return

            total_users = len(self.users)
            for i, user in enumerate(self.users, 1):
                if not self._is_running:
                    self.log("⏹ İşlem kullanıcı isteğiyle durduruldu.")
                    break
                self.process_single_user(driver, user)
                # progressSignal.emit yerine sadece hesapla
                progress = int((i / total_users) * 100)

            self.log("📩 Tüm mesajlar başarıyla gönderildi, yeni döngü başlatılıyor...")


        except Exception as e:
            self.log(f"⚠ Kritik hata: {e}")
            self.logger.error("Critical error", exc_info=True)
        finally:
            self.cleanup(driver)
            # Thread tamamlandı işareti
            if not self.is_finished:
                self.is_finished = True
                self.log("✅ MessageSender tamamlandı")

                # Callback'i çağır
                if self.callback:
                    try:
                        self.callback()
                    except Exception as callback_error:
                        self.log(f"❌ Callback hatası: {callback_error}")
            
    def setup_driver(self) -> Optional[webdriver.Chrome]:
        self.close_chrome_instances()
        if not self._is_running:
            return None

        # Chrome binary yolunu kontrol et
        if not self.check_chrome_path():
            return None

        options = Options()

        # Profil ayarları
        options.add_argument(f"user-data-dir={self.chrome_profile_path}")
        options.add_argument("profile-directory=Profile 1")
        options.binary_location = self.chrome_binary_path

        # GELİŞMİŞ ANTİ-BOT ALGILAMA ÖNLEMELERİ
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--disable-web-security")
        options.add_argument("--disable-features=VizDisplayCompositor")
        options.add_argument("--disable-ipc-flooding-protection")
        options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])
        options.add_experimental_option("useAutomationExtension", False)

        # User-Agent ayarla
        options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

        # Temel ayarlar
        options.add_argument("--start-maximized")
        options.add_argument("--disable-notifications")
        options.add_argument("--disable-popup-blocking")
        options.add_argument("--disable-gpu")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-logging")
        options.add_argument("--disable-extensions")
        options.add_argument("--ignore-certificate-errors")
        options.add_argument("--ignore-ssl-errors")

        # Ek tercihler
        prefs = {
            "profile.default_content_setting_values": {
                "notifications": 2,
                "geolocation": 2,
                "media_stream": 2,
            }
        }
        options.add_experimental_option("prefs", prefs)

        self.log(f"✅ Chrome binary yolu ayarlandı: {self.chrome_binary_path}")
        self.log(f"✅ Chrome profil yolu: {self.chrome_profile_path}, Profil: Profile 1")
        self.log(f"✅ ChromeDriver başlatılıyor: {self.driver_path}")

        service = Service(self.driver_path)
        driver = webdriver.Chrome(service=service, options=options)

        # JavaScript ile bot algılama önlemleri
        try:
            stealth_script = """
            Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
            Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
            Object.defineProperty(navigator, 'languages', {get: () => ['tr-TR', 'tr', 'en-US', 'en']});
            delete window.chrome;
            console.debug = () => {};
            """
            driver.execute_script(stealth_script)
            self.log("✅ Bot algılama önlemleri uygulandı")
        except Exception as e:
            self.log(f"⚠️ Bot algılama önlemleri uygulanamadı: {e}")

        return driver

    def close_chrome_instances(self) -> None:
        try:
            # Chrome işlemlerini zorla kapat - SIRALI ÇALIŞMA İÇİN
            import subprocess
            subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'],
                         capture_output=True, text=True)
            subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'],
                         capture_output=True, text=True)
            time.sleep(2)
            self.log("✅ Chrome işlemleri kapatıldı.")
        except Exception as e:
            self.log(f"⚠️ Chrome kapatma hatası: {e}")

    def navigate_to_messages(self, driver: webdriver.Chrome) -> None:
        try:
            # 5 saniye → 2 saniye optimizasyonu
            time.sleep(2)
            if not self._is_running:
                return
            driver.get("https://live-backstage.tiktok.com/portal/anchor/instant-messages?type=1")

            # 10 saniye sabit bekleme → WebDriverWait ile sayfa yükleme kontrolü
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[placeholder='İçerik üreticisi kullanıcı adı']"))
            )
            self.log("✅ Mesaj sayfası yüklendi")
        except Exception as e:
            self.log(f"Mesaj sayfasına gidilemedi: {e}")
            raise

    def process_single_user(self, driver: webdriver.Chrome, user: User) -> None:
        try:
            # Daha detaylı kontroller
            user_info = self.db_manager.execute_query(
                """
                SELECT 
                    message_log, 
                    timestamp
                FROM live_data 
                WHERE username=%s
                """,
                (user.username,)
            )
            
            # Hiç sonuç dönmediyse veya boş bir liste döndüyse
            if not user_info or len(user_info) == 0:
                self.log(f"[{user.username}] için bilgi bulunamadı, atlanıyor.")
                return
            
            # Kontrol: Daha önce mesaj gönderilmiş mi?
            msg_log = user_info[0].get("message_log") or ""            
            if "Mesaj Gönderildi" in msg_log:
                self.log(f"[{user.username}] Daha önce mesaj gönderilmiş, atlanıyor.")
                return
            
            # Mesaj gönderimi için zaman kontrolü
            timestamp = user_info[0].get("timestamp")
            if not self.is_recent_user(timestamp, self.cycle_start_time):
                self.log(f"[{user.username}] Bu döngüye ait değil, atlanıyor.")
                return
            
            self.log(f"🔍 İşleniyor: {user.username}")

            # Kullanıcı araması
            if not self.search_user(driver, user.username):
                self.update_message_log(user.id, "Kullanıcı adı bulunamadı")
                return
                    
            if not self._is_running:
                return

            # Uygunluk ve uyarı kontrolleri
            if self.check_ineligible_tag(driver, user.id, user.username):
                return
            
            if self.check_post_send_warnings(driver):
                return
            
            # Mesaj gönderimi
            self.send_message(driver, user.id, user.username)
            
        except Exception as e:
            self.log(f"[{user.username}] hata: {e}")
     
    def is_recent_user(self, timestamp: datetime, reference_time: Optional[datetime] = None) -> bool:
        """
        Kullanıcının belirli bir zaman aralığında olup olmadığını kontrol eder
        
        Args:
            timestamp: Kontrol edilecek zaman damgası
            reference_time: Referans olarak kullanılacak başlangıç zamanı (döngü başlangıç zamanı)
        """
        if not timestamp:
            return False
        
        # Eğer reference_time verilmişse onu kullan, yoksa şu anki zamandan 1 saat öncesini kullan
        if reference_time:
            return timestamp >= reference_time
        else:
            # Geriye dönük uyumluluk için eski davranışı koruyalım
            from datetime import timedelta
            current_time = datetime.now()
            time_threshold = current_time - timedelta(hours=1)
            return timestamp >= time_threshold
    
    def check_dm_history(self, driver: webdriver.Chrome) -> bool:
        try:
            container = WebDriverWait(driver, 5).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div.im-message-item-layout-message-content-content"))
            )
            my_messages = container.find_elements(By.CSS_SELECTOR, "div.im-message-type-text-right")
            return bool(my_messages)
        except TimeoutException:
            return False
        except Exception as e:
            self.log(f"DM geçmişi kontrolü hata: {e}")
            return False

    def search_user(self, driver: webdriver.Chrome, username: str) -> bool:
        if not self._is_running:
            return False
        try:
            input_box = WebDriverWait(driver, 10).until(
                EC.visibility_of_element_located((By.CSS_SELECTOR, "input[placeholder='İçerik üreticisi kullanıcı adı']"))
            )
            input_box.click()
            # 1 saniye → 0.3 saniye optimizasyonu
            time.sleep(0.3)
            actions = ActionChains(driver)
            actions.move_to_element(input_box).click().perform()
            # 0.5 saniye → 0.2 saniye optimizasyonu
            time.sleep(0.2)
            actions.key_down(Keys.CONTROL).send_keys('a').key_up(Keys.CONTROL).send_keys(Keys.DELETE).perform()
            # 0.5 saniye → 0.2 saniye optimizasyonu
            time.sleep(0.2)
            # 1 saniye pause → 0.5 saniye optimizasyonu
            actions.send_keys(username).pause(0.5).send_keys(Keys.ENTER).perform()
            result_element = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CLASS_NAME, "nameBox--BEINF"))
            )
            driver.execute_script("arguments[0].scrollIntoView(true);", result_element)
            # 1 saniye pause → 0.3 saniye optimizasyonu
            actions.move_to_element(result_element).pause(0.3).click().perform()
            return True
        except TimeoutException:
            self.log(f"[{username}] Arama popup'taki sonuç bulunamadı.")
            return False
        except Exception as e:
            self.log(f"[{username}] arama sonucu tıklanamadı: {e}")
            return False

    def check_ineligible_tag(self, driver: webdriver.Chrome, user_id: int, username: str) -> bool:
        if not self._is_running:
            return True
        try:
            # 10 saniye → 6 saniye optimizasyonu
            tag_elem = WebDriverWait(driver, 6).until(
                EC.visibility_of_element_located((By.CSS_SELECTOR, "div[aria-label='Tag: Uygun değil']"))
            )
            if tag_elem:
                self.log(f"[{username}] Artık uygun değil.")
                self.update_status(user_id, "Uygun değil")
                return True
        except TimeoutException:
            pass
        return False

    def check_post_send_warnings(self, driver: webdriver.Chrome) -> bool:
        """
        Mesaj gönderildikten sonra, uyarı mesajlarının DOM'da yer alıp almadığını kontrol eder.
        Tüm uyarıları tek seferde kontrol ederek 25 saniye yerine 5 saniye bekler.
        Eğer uyarı varsa, True döner.
        """
        # Tüm uyarı mesajlarını tek XPath ile kontrol et - OPTIMIZED!
        try:
            warning = WebDriverWait(driver, 5).until(
                EC.visibility_of_element_located(
                    (By.XPATH,
                     "//span[contains(text(), 'Bu kullanıcının gizlilik ayarları nedeniyle mesaj gönderilemez') or "
                     "contains(text(), 'Bu kullanıcı sizi takip etmediği için mesaj gönderilemez') or "
                     "contains(text(), 'Alıcının ayarları nedeniyle mesaj gönderilemedi') or "
                     "contains(text(), 'Sadece arkadaşlar birbirine mesaj gönderebilir') or "
                     "contains(text(), 'İletişime geçmeye çalıştığınız hesap askıya alındı')]")
                )
            )
            if warning:
                warning_text = warning.text
                self.log(f"Post-send: '{warning_text}' uyarısı bulundu.")
                return True
        except TimeoutException:
            pass
        return False
        
    def send_message(self, driver: webdriver.Chrome, user_id: int, username: str) -> None:
        if not self._is_running:
            return
        try:
            message_box = WebDriverWait(driver, 7).until(
                EC.visibility_of_element_located((By.CSS_SELECTOR,
                    "textarea[placeholder='Bu mesaj, içerik üreticisi tarafından görüntülenecek. Lütfen Topluluk Kurallarına uyun.']"
                ))
            )
            actions = ActionChains(driver)
            actions.move_to_element(message_box).click().perform()

            # Optimized: Rastgele bekleme sürelerini azalttık
            actions.send_keys("Her pazartesi").pause(random.uniform(0.3, 0.7)).perform()
            actions.send_keys(Keys.ARROW_DOWN).pause(random.uniform(0.2, 0.5)).perform()
            actions.send_keys(Keys.ENTER).pause(random.uniform(0.3, 0.7)).perform()

            send_button = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "button[data-id='backstage_IM_send_btn']"))
            )
            send_button.click()

            # Mesaj gönderildikten sonra bekleme: 3 saniye → 1.5 saniye optimizasyonu
            time.sleep(1.5)

            # Gönderim sonrası uyarı kontrolü
            if self.check_post_send_warnings(driver):
                self.update_message_log(user_id, "Mesaj gönderilemedi")
                self.log(f"[{username}] mesaj gönderildi fakat post-send uyarısı bulundu, mesaj gönderilemedi olarak güncellendi.")
                return

            # Uyarı yoksa, mesajın gönderildiğini kabul et ve veritabanını güncelle
            self.update_message_log(user_id, "Mesaj Gönderildi")
            self.log(f"[{username}] adlı kullanıcıya mesaj gönderildi.")
        except (TimeoutException, WebDriverException, ElementClickInterceptedException) as e:
            self.log(f"[{username}] Mesaj gönderme hatası: {e}")
            self.update_message_log(user_id, "Mesaj gönderilemedi")
          
    def update_message_log(self, user_id: int, message: str) -> None:
        try:
            self.db_manager.execute_query(
                "UPDATE live_data SET message_log=%s WHERE id=%s",
                (message, user_id)
            )
            self.log(f"Veritabanı güncellemesi: ID {user_id} için '{message}' olarak güncellendi.")
        except Exception as e:
            self.log(f"Veritabanı güncelleme hatası: {e}")
            self.logger.error("Database update error", exc_info=True)
        
    def cleanup(self, driver: Optional[webdriver.Chrome]) -> None:
        if driver:
            try:
                driver.quit()
                self.log("🔄 Chrome driver kapatıldı.")
            except Exception as e:
                self.log(f"❌ Driver kapatma hatası: {e}")
                self.logger.error("Driver cleanup error", exc_info=True)

        # Chrome işlemlerini zorla kapat - AŞAMA SONU
        try:
            import subprocess
            subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'],
                         capture_output=True, text=True)
            subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'],
                         capture_output=True, text=True)
            self.log("🔄 Tüm Chrome işlemleri kapatıldı")
        except:
            pass

        self._is_running = False

    def stop(self) -> None:
        self._is_running = False
        self.log("⏹ MessageSender durdurma sinyali alındı.")

    def log(self, msg: str) -> None:
        """Basit log sistemi - DUPLIKASYON ÖNLEME"""
        if msg is None:
            self.logger.error("[MESSAGE_SENDER] HATA: None değeri!")
            return
        self.logger.info(f"[MESSAGE_SENDER] {msg}")

        # UI sinyali kaldırıldı - sadece log

    def check_chrome_path(self) -> bool:
        """Chrome binary yolunun geçerli olup olmadığını kontrol eder"""
        try:
            if not os.path.exists(self.chrome_binary_path):
                self.log(f"❌ Chrome yolu bulunamadı: {self.chrome_binary_path}")
                self.log("Chrome'un doğru yolda kurulu olduğundan emin olun")
                return False
                
            if not os.path.exists(self.chrome_profile_path):
                self.log(f"❌ Chrome profil yolu bulunamadı: {self.chrome_profile_path}")
                self.log("Chrome profil klasörünün doğru yolda olduğundan emin olun")
                return False
                
            self.log("✅ Chrome yolu doğrulandı")
            return True
        except Exception as e:
            self.log(f"❌ Chrome yolu kontrol hatası: {e}")
            return False
